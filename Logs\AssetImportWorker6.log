Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.14f1 (eee1884e7226) revision 15655304'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 32577 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files\Unity\2021.3.14f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker6
-projectPath
E:/ProjectsUnity/City
-logFile
Logs/AssetImportWorker6.log
-srvPort
61627
Successfully changed project path to: E:/ProjectsUnity/City
E:/ProjectsUnity/City
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Refreshing native plugins compatible for Editor in 69.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.14f1 (eee1884e7226)
[Subsystems] Discovering subsystems at path D:/Program Files/Unity/2021.3.14f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/ProjectsUnity/City/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 Ti (ID=0x2803)
    Vendor:   NVIDIA
    VRAM:     7949 MB
    Driver:   32.0.15.6117
Initialize mono
Mono path[0] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56568
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001200 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 66.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.394 seconds
Domain Reload Profiling:
	ReloadAssembly (394ms)
		BeginReloadAssembly (39ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (299ms)
			LoadAssemblies (39ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (61ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (17ms)
			SetupLoadedEditorAssemblies (191ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (33ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (67ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (61ms)
				ProcessInitializeOnLoadMethodAttributes (29ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.002457 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 69.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.903 seconds
Domain Reload Profiling:
	ReloadAssembly (904ms)
		BeginReloadAssembly (162ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (114ms)
		EndReloadAssembly (684ms)
			LoadAssemblies (47ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (174ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (46ms)
			SetupLoadedEditorAssemblies (383ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (8ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (70ms)
				BeforeProcessingInitializeOnLoad (69ms)
				ProcessInitializeOnLoadAttributes (170ms)
				ProcessInitializeOnLoadMethodAttributes (64ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (4ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 0.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5726 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (1.2 MB). Loaded Objects now: 6167.
Memory consumption went from 221.1 MB to 219.9 MB.
Total: 2.490800 ms (FindLiveObjects: 0.292900 ms CreateObjectMapping: 0.086800 ms MarkObjects: 1.816100 ms  DeleteObjects: 0.294500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001858 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.64 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.662 seconds
Domain Reload Profiling:
	ReloadAssembly (663ms)
		BeginReloadAssembly (76ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (533ms)
			LoadAssemblies (47ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (158ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (262ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (53ms)
				ProcessInitializeOnLoadAttributes (139ms)
				ProcessInitializeOnLoadMethodAttributes (55ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6170.
Memory consumption went from 213.6 MB to 212.4 MB.
Total: 3.315500 ms (FindLiveObjects: 0.284000 ms CreateObjectMapping: 0.181700 ms MarkObjects: 2.409200 ms  DeleteObjects: 0.439600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 3834.631710 seconds.
  path: Assets/Scenes/SampleScene.unity
  artifactKey: Guid(8c9cfa26abfee488c85f1582747f6a02) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/SampleScene.unity using Guid(8c9cfa26abfee488c85f1582747f6a02) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a6718df236fc125b8f274e35db3f53d8') in 0.005454 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001834 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.636 seconds
Domain Reload Profiling:
	ReloadAssembly (636ms)
		BeginReloadAssembly (71ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (514ms)
			LoadAssemblies (44ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (145ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (255ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (47ms)
				ProcessInitializeOnLoadAttributes (132ms)
				ProcessInitializeOnLoadMethodAttributes (62ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6173.
Memory consumption went from 213.7 MB to 212.4 MB.
Total: 2.314300 ms (FindLiveObjects: 0.217500 ms CreateObjectMapping: 0.081700 ms MarkObjects: 1.682400 ms  DeleteObjects: 0.332200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 95.502134 seconds.
  path: Assets/_MyGame/RawRes
  artifactKey: Guid(ae974169982d4e540b5b37aea99efce4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes using Guid(ae974169982d4e540b5b37aea99efce4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a051eb5b57f12ba86b337dcf869d3e1c') in 0.004994 seconds 
========================================================================
Received Import Request.
  Time since last request: 20.715183 seconds.
  path: Assets/_MyGame/RawRes/animal_alice_dodo_V2.fbx
  artifactKey: Guid(643dfb718b481ba4eb0744fbe254db3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/animal_alice_dodo_V2.fbx using Guid(643dfb718b481ba4eb0744fbe254db3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1e7208b26047cd032dc34f6eed781bad') in 0.053536 seconds 
========================================================================
Received Import Request.
  Time since last request: 42.246875 seconds.
  path: Assets/_MyGame/RawRes/animal_alice_dodo_V2.png
  artifactKey: Guid(49cf613ee287dcd428dd9252589a0fe6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/animal_alice_dodo_V2.png using Guid(49cf613ee287dcd428dd9252589a0fe6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'dde7a16e7809a63660c626007f0061b7') in 0.038031 seconds 
========================================================================
Received Import Request.
  Time since last request: 429.236235 seconds.
  path: Assets/_MyGame/RawRes/animal_alice_dodo_V2.png
  artifactKey: Guid(49cf613ee287dcd428dd9252589a0fe6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/animal_alice_dodo_V2.png using Guid(49cf613ee287dcd428dd9252589a0fe6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '32b5711e6da7974c77fc24165d85103f') in 0.004207 seconds 
========================================================================
Received Import Request.
  Time since last request: 6.892231 seconds.
  path: Assets/_MyGame/RawRes/animal_alice_dodo_V2.png
  artifactKey: Guid(49cf613ee287dcd428dd9252589a0fe6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/animal_alice_dodo_V2.png using Guid(49cf613ee287dcd428dd9252589a0fe6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cb0862329e92559ec8a1b4e7909179df') in 0.014524 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001904 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.642 seconds
Domain Reload Profiling:
	ReloadAssembly (643ms)
		BeginReloadAssembly (74ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (25ms)
		EndReloadAssembly (517ms)
			LoadAssemblies (43ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (144ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (263ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (48ms)
				ProcessInitializeOnLoadAttributes (145ms)
				ProcessInitializeOnLoadMethodAttributes (57ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6197.
Memory consumption went from 217.8 MB to 216.6 MB.
Total: 3.067900 ms (FindLiveObjects: 0.228400 ms CreateObjectMapping: 0.072200 ms MarkObjects: 2.471900 ms  DeleteObjects: 0.294800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001826 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.646 seconds
Domain Reload Profiling:
	ReloadAssembly (646ms)
		BeginReloadAssembly (75ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (523ms)
			LoadAssemblies (44ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (151ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (32ms)
			SetupLoadedEditorAssemblies (261ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (51ms)
				ProcessInitializeOnLoadAttributes (143ms)
				ProcessInitializeOnLoadMethodAttributes (53ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6200.
Memory consumption went from 217.8 MB to 216.6 MB.
Total: 2.318300 ms (FindLiveObjects: 0.214800 ms CreateObjectMapping: 0.086700 ms MarkObjects: 1.694400 ms  DeleteObjects: 0.321900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 346.298259 seconds.
  path: Assets/_MyGame/RawRes/New Animator Controller.controller
  artifactKey: Guid(b3441a12525d0cb4ea7e7945e8c4c83d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/New Animator Controller.controller using Guid(b3441a12525d0cb4ea7e7945e8c4c83d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '18f5867fa136ad938960d7c56f521428') in 0.017266 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001816 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.67 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.652 seconds
Domain Reload Profiling:
	ReloadAssembly (652ms)
		BeginReloadAssembly (78ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (519ms)
			LoadAssemblies (47ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (144ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (267ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (48ms)
				ProcessInitializeOnLoadAttributes (151ms)
				ProcessInitializeOnLoadMethodAttributes (54ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6203.
Memory consumption went from 217.8 MB to 216.6 MB.
Total: 2.373900 ms (FindLiveObjects: 0.228700 ms CreateObjectMapping: 0.088300 ms MarkObjects: 1.728700 ms  DeleteObjects: 0.327600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001824 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.641 seconds
Domain Reload Profiling:
	ReloadAssembly (641ms)
		BeginReloadAssembly (72ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (519ms)
			LoadAssemblies (45ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (141ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (268ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (51ms)
				ProcessInitializeOnLoadAttributes (149ms)
				ProcessInitializeOnLoadMethodAttributes (55ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6206.
Memory consumption went from 217.7 MB to 216.5 MB.
Total: 2.457500 ms (FindLiveObjects: 0.219800 ms CreateObjectMapping: 0.090100 ms MarkObjects: 1.788700 ms  DeleteObjects: 0.358300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001878 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.660 seconds
Domain Reload Profiling:
	ReloadAssembly (661ms)
		BeginReloadAssembly (71ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (536ms)
			LoadAssemblies (45ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (149ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (272ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (52ms)
				ProcessInitializeOnLoadAttributes (154ms)
				ProcessInitializeOnLoadMethodAttributes (52ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6209.
Memory consumption went from 217.7 MB to 216.5 MB.
Total: 2.300100 ms (FindLiveObjects: 0.210000 ms CreateObjectMapping: 0.089100 ms MarkObjects: 1.689200 ms  DeleteObjects: 0.311100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 12151.754853 seconds.
  path: Assets/_MyGame/RawRes/Map
  artifactKey: Guid(4f6654c530ca2c94597575a77f87bc91) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map using Guid(4f6654c530ca2c94597575a77f87bc91) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '543bbed78e5e350e9afca2110094c6c4') in 0.009445 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.844823 seconds.
  path: Assets/_MyGame/RawRes/Map/11
  artifactKey: Guid(5caaceec87701f24cb1ef08836366950) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11 using Guid(5caaceec87701f24cb1ef08836366950) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ca95af9f2787c99133c086ec5ba2c851') in 0.001297 seconds 
========================================================================
Received Import Request.
  Time since last request: 5.835690 seconds.
  path: Assets/_MyGame/RawRes/Map/11/1.png
  artifactKey: Guid(e9bb19c17de47a1459ba72efa17c9a7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/1.png using Guid(e9bb19c17de47a1459ba72efa17c9a7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ebcf59ea3edff1b6bdc17e8efdbf9670') in 0.222413 seconds 
========================================================================
Received Import Request.
  Time since last request: 13.463742 seconds.
  path: Assets/_MyGame/Bundles
  artifactKey: Guid(0556868a748443e4c88c700a9caacf06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles using Guid(0556868a748443e4c88c700a9caacf06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b9d5d4968c616e0bab7aba6cd0a9adc8') in 0.000827 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.991508 seconds.
  path: Assets/_MyGame/Bundles/Map
  artifactKey: Guid(61f9939946768234bb6a5433b9622a6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles/Map using Guid(61f9939946768234bb6a5433b9622a6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '78eb25dc0d93e1a08bd62dc0673cad41') in 0.000658 seconds 
========================================================================
Received Import Request.
  Time since last request: 21.251126 seconds.
  path: Assets/_MyGame/Bundles/Map/map11.prefab
  artifactKey: Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles/Map/map11.prefab using Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ee83c06d03521a5d4f09210cd57fed5c') in 0.122870 seconds 
========================================================================
Received Import Request.
  Time since last request: 44.646123 seconds.
  path: Assets/_MyGame/Bundles/Map/map11.prefab
  artifactKey: Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles/Map/map11.prefab using Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7b0aa61463c0131cb3c5721ec0745f02') in 0.006488 seconds 
========================================================================
Received Import Request.
  Time since last request: 15.865667 seconds.
  path: Assets/_MyGame/Bundles/Map/map11.prefab
  artifactKey: Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles/Map/map11.prefab using Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd98686241c8739099dc0cd87cd428b9d') in 0.014452 seconds 
========================================================================
Received Import Request.
  Time since last request: 26.600952 seconds.
  path: Assets/_MyGame/Bundles/Map/Map11.prefab
  artifactKey: Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles/Map/Map11.prefab using Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'eea18f035719252ad5d9360beb5e96d7') in 0.004902 seconds 
========================================================================
Received Import Request.
  Time since last request: 65.572086 seconds.
  path: Assets/_MyGame/Bundles/Map/Map11.prefab
  artifactKey: Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles/Map/Map11.prefab using Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '230018d722bf99739bac446d7becccbc') in 0.005955 seconds 
========================================================================
Received Import Request.
  Time since last request: 3.967322 seconds.
  path: Assets/_MyGame/Bundles/Map/Map11.prefab
  artifactKey: Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles/Map/Map11.prefab using Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6249fa9c25d408005bb4f7de8f418322') in 0.005793 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.872240 seconds.
  path: Assets/_MyGame/Bundles/Map/Map11.prefab
  artifactKey: Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles/Map/Map11.prefab using Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2f23f5b6e6ae9b2d99ffbe11417c957a') in 0.006055 seconds 
========================================================================
Received Import Request.
  Time since last request: 150.742130 seconds.
  path: Assets/_MyGame/RawRes/Map/11/14.png
  artifactKey: Guid(3eba681305c4e5b4bbc5698707ea43fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/14.png using Guid(3eba681305c4e5b4bbc5698707ea43fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bcc52c89e2f376fc653e2e93bb020c65') in 0.031584 seconds 
========================================================================
Received Import Request.
  Time since last request: 660.030210 seconds.
  path: Assets/_MyGame/RawRes/Map/11/bg.png
  artifactKey: Guid(e9bb19c17de47a1459ba72efa17c9a7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/bg.png using Guid(e9bb19c17de47a1459ba72efa17c9a7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e8207350c5b9ad211cd21c54a938ef76') in 0.122836 seconds 
========================================================================
Received Import Request.
  Time since last request: 3.180442 seconds.
  path: Assets/_MyGame/RawRes/Map/11/wharf_2.png
  artifactKey: Guid(0b5ffa0e9958d454aa2898f24554677a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/wharf_2.png using Guid(0b5ffa0e9958d454aa2898f24554677a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c7a8749e1579607137af876b97804d93') in 0.014669 seconds 
========================================================================
Received Import Request.
  Time since last request: 422.940076 seconds.
  path: Assets/_MyGame/RawRes/Map/11/whatf_4.png
  artifactKey: Guid(a3e541810cf86cc4b99bafcd284c48c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/whatf_4.png using Guid(a3e541810cf86cc4b99bafcd284c48c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'aa0be69afb4e6a446b448a97fe3c8907') in 0.014640 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002242 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.693 seconds
Domain Reload Profiling:
	ReloadAssembly (693ms)
		BeginReloadAssembly (84ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (29ms)
		EndReloadAssembly (558ms)
			LoadAssemblies (48ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (155ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (35ms)
			SetupLoadedEditorAssemblies (266ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (51ms)
				ProcessInitializeOnLoadAttributes (146ms)
				ProcessInitializeOnLoadMethodAttributes (54ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5615 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6267.
Memory consumption went from 220.8 MB to 219.5 MB.
Total: 4.254000 ms (FindLiveObjects: 0.227700 ms CreateObjectMapping: 0.084200 ms MarkObjects: 3.604500 ms  DeleteObjects: 0.337000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002702 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.19 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.750 seconds
Domain Reload Profiling:
	ReloadAssembly (751ms)
		BeginReloadAssembly (75ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (627ms)
			LoadAssemblies (44ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (169ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (48ms)
			SetupLoadedEditorAssemblies (292ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (54ms)
				ProcessInitializeOnLoadAttributes (159ms)
				ProcessInitializeOnLoadMethodAttributes (59ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (12ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6271.
Memory consumption went from 221.0 MB to 219.8 MB.
Total: 3.557000 ms (FindLiveObjects: 0.229600 ms CreateObjectMapping: 0.272500 ms MarkObjects: 2.504300 ms  DeleteObjects: 0.550100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002629 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.703 seconds
Domain Reload Profiling:
	ReloadAssembly (704ms)
		BeginReloadAssembly (78ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (25ms)
		EndReloadAssembly (577ms)
			LoadAssemblies (43ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (153ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (32ms)
			SetupLoadedEditorAssemblies (287ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (69ms)
				ProcessInitializeOnLoadAttributes (144ms)
				ProcessInitializeOnLoadMethodAttributes (55ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.20 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6274.
Memory consumption went from 221.0 MB to 219.8 MB.
Total: 2.555700 ms (FindLiveObjects: 0.347300 ms CreateObjectMapping: 0.121100 ms MarkObjects: 1.765300 ms  DeleteObjects: 0.321300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002136 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.683 seconds
Domain Reload Profiling:
	ReloadAssembly (683ms)
		BeginReloadAssembly (75ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (25ms)
		EndReloadAssembly (560ms)
			LoadAssemblies (44ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (156ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (273ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (54ms)
				ProcessInitializeOnLoadAttributes (149ms)
				ProcessInitializeOnLoadMethodAttributes (55ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6277.
Memory consumption went from 221.0 MB to 219.8 MB.
Total: 2.326600 ms (FindLiveObjects: 0.234400 ms CreateObjectMapping: 0.081000 ms MarkObjects: 1.687500 ms  DeleteObjects: 0.323100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001905 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.741 seconds
Domain Reload Profiling:
	ReloadAssembly (742ms)
		BeginReloadAssembly (88ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (585ms)
			LoadAssemblies (62ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (154ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (36ms)
			SetupLoadedEditorAssemblies (280ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (54ms)
				ProcessInitializeOnLoadAttributes (152ms)
				ProcessInitializeOnLoadMethodAttributes (57ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.21 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6280.
Memory consumption went from 220.9 MB to 219.6 MB.
Total: 3.753100 ms (FindLiveObjects: 0.419900 ms CreateObjectMapping: 0.089900 ms MarkObjects: 2.672900 ms  DeleteObjects: 0.569400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002450 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.650 seconds
Domain Reload Profiling:
	ReloadAssembly (650ms)
		BeginReloadAssembly (80ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (25ms)
		EndReloadAssembly (521ms)
			LoadAssemblies (49ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (142ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (251ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (49ms)
				ProcessInitializeOnLoadAttributes (138ms)
				ProcessInitializeOnLoadMethodAttributes (50ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6283.
Memory consumption went from 221.1 MB to 219.8 MB.
Total: 2.294800 ms (FindLiveObjects: 0.219200 ms CreateObjectMapping: 0.079100 ms MarkObjects: 1.671800 ms  DeleteObjects: 0.324100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002014 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.644 seconds
Domain Reload Profiling:
	ReloadAssembly (645ms)
		BeginReloadAssembly (73ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (524ms)
			LoadAssemblies (41ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (151ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (250ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (49ms)
				ProcessInitializeOnLoadAttributes (136ms)
				ProcessInitializeOnLoadMethodAttributes (51ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6286.
Memory consumption went from 221.1 MB to 219.8 MB.
Total: 2.350100 ms (FindLiveObjects: 0.220300 ms CreateObjectMapping: 0.080700 ms MarkObjects: 1.680500 ms  DeleteObjects: 0.368100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001854 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.64 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.686 seconds
Domain Reload Profiling:
	ReloadAssembly (686ms)
		BeginReloadAssembly (82ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (551ms)
			LoadAssemblies (40ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (146ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (33ms)
			SetupLoadedEditorAssemblies (274ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (51ms)
				ProcessInitializeOnLoadAttributes (151ms)
				ProcessInitializeOnLoadMethodAttributes (57ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6289.
Memory consumption went from 221.1 MB to 219.9 MB.
Total: 3.725700 ms (FindLiveObjects: 0.244500 ms CreateObjectMapping: 0.088000 ms MarkObjects: 2.756300 ms  DeleteObjects: 0.636100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001900 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.634 seconds
Domain Reload Profiling:
	ReloadAssembly (635ms)
		BeginReloadAssembly (70ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (517ms)
			LoadAssemblies (38ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (145ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (251ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (54ms)
				ProcessInitializeOnLoadAttributes (131ms)
				ProcessInitializeOnLoadMethodAttributes (51ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6292.
Memory consumption went from 220.9 MB to 219.7 MB.
Total: 2.369600 ms (FindLiveObjects: 0.236000 ms CreateObjectMapping: 0.083300 ms MarkObjects: 1.717900 ms  DeleteObjects: 0.331800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002132 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.639 seconds
Domain Reload Profiling:
	ReloadAssembly (639ms)
		BeginReloadAssembly (70ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (522ms)
			LoadAssemblies (36ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (146ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (256ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (48ms)
				ProcessInitializeOnLoadAttributes (135ms)
				ProcessInitializeOnLoadMethodAttributes (58ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6295.
Memory consumption went from 220.9 MB to 219.7 MB.
Total: 2.568300 ms (FindLiveObjects: 0.230800 ms CreateObjectMapping: 0.086800 ms MarkObjects: 1.903100 ms  DeleteObjects: 0.346900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002118 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.651 seconds
Domain Reload Profiling:
	ReloadAssembly (651ms)
		BeginReloadAssembly (73ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (531ms)
			LoadAssemblies (39ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (146ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (254ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (49ms)
				ProcessInitializeOnLoadAttributes (138ms)
				ProcessInitializeOnLoadMethodAttributes (53ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.49 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6298.
Memory consumption went from 220.9 MB to 219.7 MB.
Total: 2.327100 ms (FindLiveObjects: 0.232000 ms CreateObjectMapping: 0.084900 ms MarkObjects: 1.674500 ms  DeleteObjects: 0.334800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001852 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.782 seconds
Domain Reload Profiling:
	ReloadAssembly (783ms)
		BeginReloadAssembly (78ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (640ms)
			LoadAssemblies (47ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (190ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (303ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (55ms)
				ProcessInitializeOnLoadAttributes (166ms)
				ProcessInitializeOnLoadMethodAttributes (65ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6301.
Memory consumption went from 220.9 MB to 219.7 MB.
Total: 3.667200 ms (FindLiveObjects: 0.389500 ms CreateObjectMapping: 0.126500 ms MarkObjects: 2.617800 ms  DeleteObjects: 0.532500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001849 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.668 seconds
Domain Reload Profiling:
	ReloadAssembly (669ms)
		BeginReloadAssembly (80ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (30ms)
		EndReloadAssembly (542ms)
			LoadAssemblies (43ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (152ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (259ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (49ms)
				ProcessInitializeOnLoadAttributes (139ms)
				ProcessInitializeOnLoadMethodAttributes (56ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6304.
Memory consumption went from 221.0 MB to 219.7 MB.
Total: 2.315500 ms (FindLiveObjects: 0.226900 ms CreateObjectMapping: 0.083300 ms MarkObjects: 1.677400 ms  DeleteObjects: 0.327400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002663 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.65 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.675 seconds
Domain Reload Profiling:
	ReloadAssembly (675ms)
		BeginReloadAssembly (72ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (555ms)
			LoadAssemblies (41ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (149ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (274ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (53ms)
				ProcessInitializeOnLoadAttributes (151ms)
				ProcessInitializeOnLoadMethodAttributes (55ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6307.
Memory consumption went from 221.0 MB to 219.8 MB.
Total: 2.550400 ms (FindLiveObjects: 0.229600 ms CreateObjectMapping: 0.097900 ms MarkObjects: 1.879600 ms  DeleteObjects: 0.342600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001906 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.688 seconds
Domain Reload Profiling:
	ReloadAssembly (689ms)
		BeginReloadAssembly (72ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (567ms)
			LoadAssemblies (40ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (160ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (273ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (53ms)
				ProcessInitializeOnLoadAttributes (148ms)
				ProcessInitializeOnLoadMethodAttributes (56ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6310.
Memory consumption went from 221.0 MB to 219.8 MB.
Total: 3.208100 ms (FindLiveObjects: 0.304600 ms CreateObjectMapping: 0.076600 ms MarkObjects: 2.483300 ms  DeleteObjects: 0.343100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002181 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.658 seconds
Domain Reload Profiling:
	ReloadAssembly (658ms)
		BeginReloadAssembly (72ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (535ms)
			LoadAssemblies (38ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (146ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (260ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (51ms)
				ProcessInitializeOnLoadAttributes (135ms)
				ProcessInitializeOnLoadMethodAttributes (60ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6313.
Memory consumption went from 221.0 MB to 219.8 MB.
Total: 2.406400 ms (FindLiveObjects: 0.238200 ms CreateObjectMapping: 0.084500 ms MarkObjects: 1.731500 ms  DeleteObjects: 0.351400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002222 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.78 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.672 seconds
Domain Reload Profiling:
	ReloadAssembly (672ms)
		BeginReloadAssembly (76ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (26ms)
		EndReloadAssembly (544ms)
			LoadAssemblies (42ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (146ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (269ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (51ms)
				ProcessInitializeOnLoadAttributes (147ms)
				ProcessInitializeOnLoadMethodAttributes (54ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.21 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6316.
Memory consumption went from 221.2 MB to 220.0 MB.
Total: 2.387400 ms (FindLiveObjects: 0.233100 ms CreateObjectMapping: 0.085900 ms MarkObjects: 1.736200 ms  DeleteObjects: 0.331500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002247 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.655 seconds
Domain Reload Profiling:
	ReloadAssembly (656ms)
		BeginReloadAssembly (75ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (25ms)
		EndReloadAssembly (526ms)
			LoadAssemblies (42ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (144ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (253ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (50ms)
				ProcessInitializeOnLoadAttributes (138ms)
				ProcessInitializeOnLoadMethodAttributes (51ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6319.
Memory consumption went from 221.2 MB to 220.0 MB.
Total: 2.359800 ms (FindLiveObjects: 0.226100 ms CreateObjectMapping: 0.082100 ms MarkObjects: 1.695400 ms  DeleteObjects: 0.355600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001797 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.628 seconds
Domain Reload Profiling:
	ReloadAssembly (629ms)
		BeginReloadAssembly (69ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (21ms)
		EndReloadAssembly (513ms)
			LoadAssemblies (38ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (144ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (249ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (48ms)
				ProcessInitializeOnLoadAttributes (134ms)
				ProcessInitializeOnLoadMethodAttributes (53ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6322.
Memory consumption went from 221.2 MB to 220.0 MB.
Total: 2.377300 ms (FindLiveObjects: 0.268200 ms CreateObjectMapping: 0.088000 ms MarkObjects: 1.693300 ms  DeleteObjects: 0.327200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002127 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.640 seconds
Domain Reload Profiling:
	ReloadAssembly (641ms)
		BeginReloadAssembly (72ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (518ms)
			LoadAssemblies (37ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (150ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (246ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (48ms)
				ProcessInitializeOnLoadAttributes (134ms)
				ProcessInitializeOnLoadMethodAttributes (50ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6325.
Memory consumption went from 221.0 MB to 219.8 MB.
Total: 2.340800 ms (FindLiveObjects: 0.236400 ms CreateObjectMapping: 0.081200 ms MarkObjects: 1.693600 ms  DeleteObjects: 0.329000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002077 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.648 seconds
Domain Reload Profiling:
	ReloadAssembly (648ms)
		BeginReloadAssembly (82ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (519ms)
			LoadAssemblies (45ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (150ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (247ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (52ms)
				ProcessInitializeOnLoadAttributes (133ms)
				ProcessInitializeOnLoadMethodAttributes (49ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6328.
Memory consumption went from 221.2 MB to 220.0 MB.
Total: 2.399400 ms (FindLiveObjects: 0.236800 ms CreateObjectMapping: 0.105500 ms MarkObjects: 1.723100 ms  DeleteObjects: 0.333400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001950 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.648 seconds
Domain Reload Profiling:
	ReloadAssembly (649ms)
		BeginReloadAssembly (72ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (21ms)
		EndReloadAssembly (524ms)
			LoadAssemblies (41ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (151ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (245ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (48ms)
				ProcessInitializeOnLoadAttributes (132ms)
				ProcessInitializeOnLoadMethodAttributes (49ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6331.
Memory consumption went from 221.3 MB to 220.0 MB.
Total: 2.342600 ms (FindLiveObjects: 0.229800 ms CreateObjectMapping: 0.083600 ms MarkObjects: 1.707400 ms  DeleteObjects: 0.321200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001854 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.58 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.708 seconds
Domain Reload Profiling:
	ReloadAssembly (709ms)
		BeginReloadAssembly (84ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (572ms)
			LoadAssemblies (44ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (153ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (40ms)
			SetupLoadedEditorAssemblies (273ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (52ms)
				ProcessInitializeOnLoadAttributes (155ms)
				ProcessInitializeOnLoadMethodAttributes (50ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.20 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6334.
Memory consumption went from 221.3 MB to 220.0 MB.
Total: 3.778600 ms (FindLiveObjects: 0.368800 ms CreateObjectMapping: 0.124900 ms MarkObjects: 2.763700 ms  DeleteObjects: 0.520500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001889 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.646 seconds
Domain Reload Profiling:
	ReloadAssembly (647ms)
		BeginReloadAssembly (71ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (21ms)
		EndReloadAssembly (528ms)
			LoadAssemblies (41ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (147ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (252ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (49ms)
				ProcessInitializeOnLoadAttributes (136ms)
				ProcessInitializeOnLoadMethodAttributes (51ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6337.
Memory consumption went from 221.1 MB to 219.9 MB.
Total: 2.382600 ms (FindLiveObjects: 0.276100 ms CreateObjectMapping: 0.085100 ms MarkObjects: 1.675700 ms  DeleteObjects: 0.345300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002121 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.64 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.671 seconds
Domain Reload Profiling:
	ReloadAssembly (672ms)
		BeginReloadAssembly (77ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (25ms)
		EndReloadAssembly (547ms)
			LoadAssemblies (40ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (146ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (271ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (53ms)
				ProcessInitializeOnLoadAttributes (148ms)
				ProcessInitializeOnLoadMethodAttributes (55ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6340.
Memory consumption went from 221.1 MB to 219.9 MB.
Total: 2.505900 ms (FindLiveObjects: 0.246900 ms CreateObjectMapping: 0.096300 ms MarkObjects: 1.790500 ms  DeleteObjects: 0.371500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002073 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.73 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.657 seconds
Domain Reload Profiling:
	ReloadAssembly (657ms)
		BeginReloadAssembly (77ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (26ms)
		EndReloadAssembly (532ms)
			LoadAssemblies (44ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (144ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (260ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (54ms)
				ProcessInitializeOnLoadAttributes (135ms)
				ProcessInitializeOnLoadMethodAttributes (56ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6343.
Memory consumption went from 221.1 MB to 219.9 MB.
Total: 2.382100 ms (FindLiveObjects: 0.228300 ms CreateObjectMapping: 0.083700 ms MarkObjects: 1.735000 ms  DeleteObjects: 0.334500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002200 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.67 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.682 seconds
Domain Reload Profiling:
	ReloadAssembly (682ms)
		BeginReloadAssembly (78ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (553ms)
			LoadAssemblies (43ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (146ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (277ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (52ms)
				ProcessInitializeOnLoadAttributes (152ms)
				ProcessInitializeOnLoadMethodAttributes (58ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6346.
Memory consumption went from 221.1 MB to 219.9 MB.
Total: 3.698600 ms (FindLiveObjects: 0.372500 ms CreateObjectMapping: 0.128200 ms MarkObjects: 2.638600 ms  DeleteObjects: 0.558200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002513 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.67 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.670 seconds
Domain Reload Profiling:
	ReloadAssembly (671ms)
		BeginReloadAssembly (74ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (548ms)
			LoadAssemblies (41ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (152ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (266ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (51ms)
				ProcessInitializeOnLoadAttributes (150ms)
				ProcessInitializeOnLoadMethodAttributes (51ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6349.
Memory consumption went from 221.3 MB to 220.1 MB.
Total: 2.344600 ms (FindLiveObjects: 0.229400 ms CreateObjectMapping: 0.078200 ms MarkObjects: 1.707300 ms  DeleteObjects: 0.329100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002103 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.656 seconds
Domain Reload Profiling:
	ReloadAssembly (657ms)
		BeginReloadAssembly (73ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (534ms)
			LoadAssemblies (40ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (154ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (255ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (51ms)
				ProcessInitializeOnLoadAttributes (138ms)
				ProcessInitializeOnLoadMethodAttributes (52ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6352.
Memory consumption went from 221.3 MB to 220.1 MB.
Total: 2.386300 ms (FindLiveObjects: 0.225300 ms CreateObjectMapping: 0.082600 ms MarkObjects: 1.744600 ms  DeleteObjects: 0.333200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002005 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.77 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.754 seconds
Domain Reload Profiling:
	ReloadAssembly (755ms)
		BeginReloadAssembly (91ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (28ms)
		EndReloadAssembly (609ms)
			LoadAssemblies (55ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (154ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (39ms)
			SetupLoadedEditorAssemblies (287ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (53ms)
				ProcessInitializeOnLoadAttributes (158ms)
				ProcessInitializeOnLoadMethodAttributes (60ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6355.
Memory consumption went from 221.4 MB to 220.1 MB.
Total: 3.287800 ms (FindLiveObjects: 0.373000 ms CreateObjectMapping: 0.123500 ms MarkObjects: 2.464200 ms  DeleteObjects: 0.326300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002028 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.642 seconds
Domain Reload Profiling:
	ReloadAssembly (643ms)
		BeginReloadAssembly (72ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (522ms)
			LoadAssemblies (41ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (145ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (251ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (50ms)
				ProcessInitializeOnLoadAttributes (137ms)
				ProcessInitializeOnLoadMethodAttributes (50ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6358.
Memory consumption went from 221.2 MB to 219.9 MB.
Total: 2.646000 ms (FindLiveObjects: 0.242600 ms CreateObjectMapping: 0.113800 ms MarkObjects: 1.913000 ms  DeleteObjects: 0.375900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001896 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.683 seconds
Domain Reload Profiling:
	ReloadAssembly (684ms)
		BeginReloadAssembly (79ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (28ms)
		EndReloadAssembly (555ms)
			LoadAssemblies (39ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (163ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (268ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (53ms)
				ProcessInitializeOnLoadAttributes (148ms)
				ProcessInitializeOnLoadMethodAttributes (53ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6361.
Memory consumption went from 221.2 MB to 219.9 MB.
Total: 2.457700 ms (FindLiveObjects: 0.237000 ms CreateObjectMapping: 0.082800 ms MarkObjects: 1.768100 ms  DeleteObjects: 0.369000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001834 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.89 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.648 seconds
Domain Reload Profiling:
	ReloadAssembly (648ms)
		BeginReloadAssembly (73ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (526ms)
			LoadAssemblies (38ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (149ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (253ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (49ms)
				ProcessInitializeOnLoadAttributes (137ms)
				ProcessInitializeOnLoadMethodAttributes (50ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6364.
Memory consumption went from 221.2 MB to 220.0 MB.
Total: 2.664700 ms (FindLiveObjects: 0.227700 ms CreateObjectMapping: 0.081600 ms MarkObjects: 1.880600 ms  DeleteObjects: 0.474200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002135 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.689 seconds
Domain Reload Profiling:
	ReloadAssembly (690ms)
		BeginReloadAssembly (77ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (559ms)
			LoadAssemblies (43ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (148ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (277ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (52ms)
				ProcessInitializeOnLoadAttributes (153ms)
				ProcessInitializeOnLoadMethodAttributes (56ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6367.
Memory consumption went from 221.2 MB to 220.0 MB.
Total: 3.184900 ms (FindLiveObjects: 0.318100 ms CreateObjectMapping: 0.084500 ms MarkObjects: 2.220200 ms  DeleteObjects: 0.561400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002583 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.688 seconds
Domain Reload Profiling:
	ReloadAssembly (689ms)
		BeginReloadAssembly (82ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (556ms)
			LoadAssemblies (41ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (161ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (266ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (54ms)
				ProcessInitializeOnLoadAttributes (143ms)
				ProcessInitializeOnLoadMethodAttributes (54ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.51 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6370.
Memory consumption went from 221.2 MB to 220.0 MB.
Total: 2.533100 ms (FindLiveObjects: 0.366600 ms CreateObjectMapping: 0.089100 ms MarkObjects: 1.725200 ms  DeleteObjects: 0.351800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002158 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.658 seconds
Domain Reload Profiling:
	ReloadAssembly (659ms)
		BeginReloadAssembly (74ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (533ms)
			LoadAssemblies (39ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (147ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (33ms)
			SetupLoadedEditorAssemblies (260ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (49ms)
				ProcessInitializeOnLoadAttributes (143ms)
				ProcessInitializeOnLoadMethodAttributes (52ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.44 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6373.
Memory consumption went from 221.2 MB to 220.0 MB.
Total: 2.361700 ms (FindLiveObjects: 0.246800 ms CreateObjectMapping: 0.089100 ms MarkObjects: 1.688900 ms  DeleteObjects: 0.336300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001914 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.79 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.702 seconds
Domain Reload Profiling:
	ReloadAssembly (702ms)
		BeginReloadAssembly (73ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (580ms)
			LoadAssemblies (42ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (167ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (285ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (54ms)
				ProcessInitializeOnLoadAttributes (158ms)
				ProcessInitializeOnLoadMethodAttributes (56ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.31 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6376.
Memory consumption went from 221.3 MB to 220.0 MB.
Total: 2.694900 ms (FindLiveObjects: 0.240700 ms CreateObjectMapping: 0.084200 ms MarkObjects: 1.996900 ms  DeleteObjects: 0.372400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002200 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.37 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.808 seconds
Domain Reload Profiling:
	ReloadAssembly (809ms)
		BeginReloadAssembly (92ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (31ms)
		EndReloadAssembly (647ms)
			LoadAssemblies (48ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (180ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (35ms)
			SetupLoadedEditorAssemblies (319ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (57ms)
				ProcessInitializeOnLoadAttributes (177ms)
				ProcessInitializeOnLoadMethodAttributes (68ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.75 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6379.
Memory consumption went from 221.3 MB to 220.0 MB.
Total: 2.776200 ms (FindLiveObjects: 0.422500 ms CreateObjectMapping: 0.153100 ms MarkObjects: 1.860400 ms  DeleteObjects: 0.339300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001933 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.78 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.668 seconds
Domain Reload Profiling:
	ReloadAssembly (668ms)
		BeginReloadAssembly (72ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (544ms)
			LoadAssemblies (38ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (148ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (258ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (50ms)
				ProcessInitializeOnLoadAttributes (140ms)
				ProcessInitializeOnLoadMethodAttributes (53ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (13ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6382.
Memory consumption went from 221.3 MB to 220.1 MB.
Total: 2.577500 ms (FindLiveObjects: 0.241100 ms CreateObjectMapping: 0.094800 ms MarkObjects: 1.872800 ms  DeleteObjects: 0.368100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002131 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.58 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.808 seconds
Domain Reload Profiling:
	ReloadAssembly (810ms)
		BeginReloadAssembly (80ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (12ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (21ms)
		EndReloadAssembly (665ms)
			LoadAssemblies (39ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (191ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (324ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (59ms)
				ProcessInitializeOnLoadAttributes (183ms)
				ProcessInitializeOnLoadMethodAttributes (65ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (12ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6385.
Memory consumption went from 221.3 MB to 220.1 MB.
Total: 3.909300 ms (FindLiveObjects: 0.537800 ms CreateObjectMapping: 0.163100 ms MarkObjects: 2.651300 ms  DeleteObjects: 0.556200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001932 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.660 seconds
Domain Reload Profiling:
	ReloadAssembly (661ms)
		BeginReloadAssembly (76ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (26ms)
		EndReloadAssembly (536ms)
			LoadAssemblies (39ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (148ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (257ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (51ms)
				ProcessInitializeOnLoadAttributes (140ms)
				ProcessInitializeOnLoadMethodAttributes (51ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.38 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6388.
Memory consumption went from 221.3 MB to 220.1 MB.
Total: 2.399800 ms (FindLiveObjects: 0.246300 ms CreateObjectMapping: 0.084200 ms MarkObjects: 1.710700 ms  DeleteObjects: 0.357700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001911 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.67 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.697 seconds
Domain Reload Profiling:
	ReloadAssembly (697ms)
		BeginReloadAssembly (87ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (35ms)
		EndReloadAssembly (559ms)
			LoadAssemblies (44ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (157ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (269ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (53ms)
				ProcessInitializeOnLoadAttributes (147ms)
				ProcessInitializeOnLoadMethodAttributes (53ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6391.
Memory consumption went from 221.3 MB to 220.1 MB.
Total: 2.450500 ms (FindLiveObjects: 0.242400 ms CreateObjectMapping: 0.106200 ms MarkObjects: 1.748900 ms  DeleteObjects: 0.352300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.003486 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.813 seconds
Domain Reload Profiling:
	ReloadAssembly (814ms)
		BeginReloadAssembly (86ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (29ms)
		EndReloadAssembly (670ms)
			LoadAssemblies (50ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (160ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (37ms)
			SetupLoadedEditorAssemblies (346ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (70ms)
				ProcessInitializeOnLoadAttributes (179ms)
				ProcessInitializeOnLoadMethodAttributes (75ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (13ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6394.
Memory consumption went from 221.5 MB to 220.3 MB.
Total: 3.275800 ms (FindLiveObjects: 0.303800 ms CreateObjectMapping: 0.082900 ms MarkObjects: 2.312600 ms  DeleteObjects: 0.575800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002258 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.679 seconds
Domain Reload Profiling:
	ReloadAssembly (679ms)
		BeginReloadAssembly (75ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (552ms)
			LoadAssemblies (41ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (146ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (37ms)
			SetupLoadedEditorAssemblies (270ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (51ms)
				ProcessInitializeOnLoadAttributes (152ms)
				ProcessInitializeOnLoadMethodAttributes (52ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.64 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6397.
Memory consumption went from 221.5 MB to 220.3 MB.
Total: 2.357700 ms (FindLiveObjects: 0.233700 ms CreateObjectMapping: 0.085100 ms MarkObjects: 1.689500 ms  DeleteObjects: 0.348600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002434 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.92 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.644 seconds
Domain Reload Profiling:
	ReloadAssembly (645ms)
		BeginReloadAssembly (71ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (525ms)
			LoadAssemblies (40ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (144ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (254ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (50ms)
				ProcessInitializeOnLoadAttributes (136ms)
				ProcessInitializeOnLoadMethodAttributes (53ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6400.
Memory consumption went from 221.5 MB to 220.3 MB.
Total: 2.379700 ms (FindLiveObjects: 0.230800 ms CreateObjectMapping: 0.082400 ms MarkObjects: 1.729300 ms  DeleteObjects: 0.336500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001862 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.671 seconds
Domain Reload Profiling:
	ReloadAssembly (671ms)
		BeginReloadAssembly (75ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (539ms)
			LoadAssemblies (41ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (147ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (264ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (52ms)
				ProcessInitializeOnLoadAttributes (144ms)
				ProcessInitializeOnLoadMethodAttributes (53ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6403.
Memory consumption went from 221.4 MB to 220.1 MB.
Total: 2.483600 ms (FindLiveObjects: 0.232200 ms CreateObjectMapping: 0.083200 ms MarkObjects: 1.839400 ms  DeleteObjects: 0.328300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002154 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.681 seconds
Domain Reload Profiling:
	ReloadAssembly (682ms)
		BeginReloadAssembly (72ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (555ms)
			LoadAssemblies (38ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (149ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (277ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (54ms)
				ProcessInitializeOnLoadAttributes (150ms)
				ProcessInitializeOnLoadMethodAttributes (57ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.38 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6406.
Memory consumption went from 221.6 MB to 220.3 MB.
Total: 2.939900 ms (FindLiveObjects: 0.248900 ms CreateObjectMapping: 0.086200 ms MarkObjects: 2.183800 ms  DeleteObjects: 0.420400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002054 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.649 seconds
Domain Reload Profiling:
	ReloadAssembly (650ms)
		BeginReloadAssembly (73ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (529ms)
			LoadAssemblies (42ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (150ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (257ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (49ms)
				ProcessInitializeOnLoadAttributes (132ms)
				ProcessInitializeOnLoadMethodAttributes (59ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6409.
Memory consumption went from 221.6 MB to 220.3 MB.
Total: 2.458100 ms (FindLiveObjects: 0.287200 ms CreateObjectMapping: 0.089400 ms MarkObjects: 1.741900 ms  DeleteObjects: 0.339100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002109 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.73 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.638 seconds
Domain Reload Profiling:
	ReloadAssembly (639ms)
		BeginReloadAssembly (73ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (518ms)
			LoadAssemblies (40ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (144ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (251ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (49ms)
				ProcessInitializeOnLoadAttributes (136ms)
				ProcessInitializeOnLoadMethodAttributes (52ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6412.
Memory consumption went from 221.6 MB to 220.4 MB.
Total: 2.389300 ms (FindLiveObjects: 0.245600 ms CreateObjectMapping: 0.087300 ms MarkObjects: 1.719100 ms  DeleteObjects: 0.336400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002019 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.672 seconds
Domain Reload Profiling:
	ReloadAssembly (673ms)
		BeginReloadAssembly (71ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (21ms)
		EndReloadAssembly (553ms)
			LoadAssemblies (39ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (154ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (33ms)
			SetupLoadedEditorAssemblies (268ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (54ms)
				ProcessInitializeOnLoadAttributes (145ms)
				ProcessInitializeOnLoadMethodAttributes (55ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6415.
Memory consumption went from 221.4 MB to 220.2 MB.
Total: 2.358100 ms (FindLiveObjects: 0.232900 ms CreateObjectMapping: 0.083400 ms MarkObjects: 1.710000 ms  DeleteObjects: 0.331000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002027 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.678 seconds
Domain Reload Profiling:
	ReloadAssembly (678ms)
		BeginReloadAssembly (74ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (552ms)
			LoadAssemblies (41ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (147ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (36ms)
			SetupLoadedEditorAssemblies (273ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (53ms)
				ProcessInitializeOnLoadAttributes (147ms)
				ProcessInitializeOnLoadMethodAttributes (57ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6418.
Memory consumption went from 221.4 MB to 220.2 MB.
Total: 2.340200 ms (FindLiveObjects: 0.225800 ms CreateObjectMapping: 0.080300 ms MarkObjects: 1.708900 ms  DeleteObjects: 0.324500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002051 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.643 seconds
Domain Reload Profiling:
	ReloadAssembly (643ms)
		BeginReloadAssembly (71ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (21ms)
		EndReloadAssembly (518ms)
			LoadAssemblies (38ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (145ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (253ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (50ms)
				ProcessInitializeOnLoadAttributes (133ms)
				ProcessInitializeOnLoadMethodAttributes (55ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.33 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6421.
Memory consumption went from 221.4 MB to 220.2 MB.
Total: 2.626100 ms (FindLiveObjects: 0.268500 ms CreateObjectMapping: 0.177500 ms MarkObjects: 1.816200 ms  DeleteObjects: 0.363200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002237 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.647 seconds
Domain Reload Profiling:
	ReloadAssembly (647ms)
		BeginReloadAssembly (74ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (525ms)
			LoadAssemblies (40ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (148ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (252ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (49ms)
				ProcessInitializeOnLoadAttributes (135ms)
				ProcessInitializeOnLoadMethodAttributes (53ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6424.
Memory consumption went from 221.4 MB to 220.2 MB.
Total: 2.346000 ms (FindLiveObjects: 0.233900 ms CreateObjectMapping: 0.082800 ms MarkObjects: 1.698700 ms  DeleteObjects: 0.329700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002031 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.650 seconds
Domain Reload Profiling:
	ReloadAssembly (650ms)
		BeginReloadAssembly (72ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (529ms)
			LoadAssemblies (40ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (151ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (255ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (50ms)
				ProcessInitializeOnLoadAttributes (134ms)
				ProcessInitializeOnLoadMethodAttributes (56ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6427.
Memory consumption went from 221.5 MB to 220.2 MB.
Total: 2.479300 ms (FindLiveObjects: 0.237600 ms CreateObjectMapping: 0.084700 ms MarkObjects: 1.723800 ms  DeleteObjects: 0.432400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002117 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.69 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.683 seconds
Domain Reload Profiling:
	ReloadAssembly (683ms)
		BeginReloadAssembly (76ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (553ms)
			LoadAssemblies (41ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (152ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (274ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (52ms)
				ProcessInitializeOnLoadAttributes (149ms)
				ProcessInitializeOnLoadMethodAttributes (57ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.54 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6430.
Memory consumption went from 221.5 MB to 220.2 MB.
Total: 2.356000 ms (FindLiveObjects: 0.237900 ms CreateObjectMapping: 0.084400 ms MarkObjects: 1.699900 ms  DeleteObjects: 0.333200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002482 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.635 seconds
Domain Reload Profiling:
	ReloadAssembly (635ms)
		BeginReloadAssembly (73ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (514ms)
			LoadAssemblies (41ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (145ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (244ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (47ms)
				ProcessInitializeOnLoadAttributes (130ms)
				ProcessInitializeOnLoadMethodAttributes (52ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6433.
Memory consumption went from 221.5 MB to 220.2 MB.
Total: 2.506400 ms (FindLiveObjects: 0.236300 ms CreateObjectMapping: 0.086700 ms MarkObjects: 1.844800 ms  DeleteObjects: 0.338000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001945 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.818 seconds
Domain Reload Profiling:
	ReloadAssembly (819ms)
		BeginReloadAssembly (98ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (30ms)
		EndReloadAssembly (656ms)
			LoadAssemblies (55ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (182ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (40ms)
			SetupLoadedEditorAssemblies (319ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (54ms)
				ProcessInitializeOnLoadAttributes (183ms)
				ProcessInitializeOnLoadMethodAttributes (66ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (13ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6436.
Memory consumption went from 221.5 MB to 220.3 MB.
Total: 2.642500 ms (FindLiveObjects: 0.268600 ms CreateObjectMapping: 0.096600 ms MarkObjects: 1.818600 ms  DeleteObjects: 0.458100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002152 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.685 seconds
Domain Reload Profiling:
	ReloadAssembly (685ms)
		BeginReloadAssembly (82ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (30ms)
		EndReloadAssembly (551ms)
			LoadAssemblies (48ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (148ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (35ms)
			SetupLoadedEditorAssemblies (267ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (52ms)
				ProcessInitializeOnLoadAttributes (144ms)
				ProcessInitializeOnLoadMethodAttributes (55ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6439.
Memory consumption went from 221.5 MB to 220.3 MB.
Total: 2.416200 ms (FindLiveObjects: 0.243900 ms CreateObjectMapping: 0.084900 ms MarkObjects: 1.745500 ms  DeleteObjects: 0.341100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002190 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.689 seconds
Domain Reload Profiling:
	ReloadAssembly (690ms)
		BeginReloadAssembly (81ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (29ms)
		EndReloadAssembly (560ms)
			LoadAssemblies (45ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (154ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (35ms)
			SetupLoadedEditorAssemblies (270ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (50ms)
				ProcessInitializeOnLoadAttributes (148ms)
				ProcessInitializeOnLoadMethodAttributes (58ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5617 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6442.
Memory consumption went from 221.5 MB to 220.3 MB.
Total: 2.594900 ms (FindLiveObjects: 0.252900 ms CreateObjectMapping: 0.087900 ms MarkObjects: 1.888400 ms  DeleteObjects: 0.364600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 5492.583925 seconds.
  path: Assets/_MyGame/Bundles/Map/Map11.prefab
  artifactKey: Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Bundles/Map/Map11.prefab using Guid(5b753a5a20778624e9b05a67e11588c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '551a8111bbe40da0caa2aa0f862f99cf') in 0.123589 seconds 
========================================================================
Received Import Request.
  Time since last request: 11.432258 seconds.
  path: Assets/_MyGame/RawRes/Map/11.spriteatlas
  artifactKey: Guid(1ca195218b80e6f4ea852e36e0bd90d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11.spriteatlas using Guid(1ca195218b80e6f4ea852e36e0bd90d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e7f0c085deede7ac437c61a8d41102dd') in 0.003429 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001853 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.65 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.689 seconds
Domain Reload Profiling:
	ReloadAssembly (689ms)
		BeginReloadAssembly (82ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (28ms)
		EndReloadAssembly (556ms)
			LoadAssemblies (40ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (150ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (276ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (61ms)
				ProcessInitializeOnLoadAttributes (144ms)
				ProcessInitializeOnLoadMethodAttributes (56ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5616 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6486.
Memory consumption went from 221.5 MB to 220.3 MB.
Total: 2.381200 ms (FindLiveObjects: 0.244900 ms CreateObjectMapping: 0.081500 ms MarkObjects: 1.712600 ms  DeleteObjects: 0.341600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1346.747092 seconds.
  path: Assets/_MyGame/RawRes/Map/11/boat.png
  artifactKey: Guid(ea458f69e735d794cadfd5adac896ebb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/boat.png using Guid(ea458f69e735d794cadfd5adac896ebb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '919b72823ef9171c1506d29730c42ace') in 0.977554 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/_MyGame/RawRes/Map/11/stone3.png
  artifactKey: Guid(76da2a7af8978dc4a93afbbea21123f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/stone3.png using Guid(76da2a7af8978dc4a93afbbea21123f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '450a46a34316259351f1107746f2610c') in 0.039502 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/_MyGame/RawRes/Map/11/house3.png
  artifactKey: Guid(427e30a5db4357542a6a24285517c4cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/house3.png using Guid(427e30a5db4357542a6a24285517c4cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8e0f997bf002a408f977bf0e3a47fd44') in 0.018624 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/RawRes/Map/11/tree2.png
  artifactKey: Guid(2fe209f45d914674ca5dc6ef54455004) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/tree2.png using Guid(2fe209f45d914674ca5dc6ef54455004) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '980003b97fe5f8373816451e2d26031a') in 0.027630 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/_MyGame/RawRes/Map/11/house2.png
  artifactKey: Guid(cd0a86f9a34679e43bec3dabfaea76a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/house2.png using Guid(cd0a86f9a34679e43bec3dabfaea76a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '837b738aa37fa1441c23ffa1b374597e') in 0.013887 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/_MyGame/RawRes/Map/11/stone2.png
  artifactKey: Guid(954e9bda92a34ea49adb35b07fe8ed6b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/stone2.png using Guid(954e9bda92a34ea49adb35b07fe8ed6b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '109f12a85ed56f85eb6a33edd0b0014f') in 0.011948 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/_MyGame/RawRes/Map/11/tree1.png
  artifactKey: Guid(974a7b998da8ea547afe2d862c15c616) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/tree1.png using Guid(974a7b998da8ea547afe2d862c15c616) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fcef7ab52cd60b62a9d26bbbda77a8dd') in 0.027963 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/RawRes/Map/11/stone1.png
  artifactKey: Guid(86522361abb38104c9afe207be85981d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/RawRes/Map/11/stone1.png using Guid(86522361abb38104c9afe207be85981d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2003b190fab468599bb09913a807a005') in 0.012194 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0