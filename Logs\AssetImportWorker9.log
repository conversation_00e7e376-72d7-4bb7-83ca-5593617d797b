Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.14f1 (eee1884e7226) revision 15655304'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 32577 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files\Unity\2021.3.14f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker9
-projectPath
E:/ProjectsUnity/City
-logFile
Logs/AssetImportWorker9.log
-srvPort
61627
Successfully changed project path to: E:/ProjectsUnity/City
E:/ProjectsUnity/City
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Refreshing native plugins compatible for Editor in 67.68 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.14f1 (eee1884e7226)
[Subsystems] Discovering subsystems at path D:/Program Files/Unity/2021.3.14f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/ProjectsUnity/City/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 Ti (ID=0x2803)
    Vendor:   NVIDIA
    VRAM:     7949 MB
    Driver:   32.0.15.6117
Initialize mono
Mono path[0] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56676
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003215 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 72.49 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.525 seconds
Domain Reload Profiling:
	ReloadAssembly (525ms)
		BeginReloadAssembly (110ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (331ms)
			LoadAssemblies (110ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (75ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (19ms)
			SetupLoadedEditorAssemblies (205ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (37ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (73ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (63ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.011582 seconds.
