Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.14f1 (eee1884e7226) revision 15655304'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 32577 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files\Unity\2021.3.14f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
E:/ProjectsUnity/City
-logFile
Logs/AssetImportWorker0.log
-srvPort
52262
Successfully changed project path to: E:/ProjectsUnity/City
E:/ProjectsUnity/City
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Refreshing native plugins compatible for Editor in 66.38 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.14f1 (eee1884e7226)
[Subsystems] Discovering subsystems at path D:/Program Files/Unity/2021.3.14f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/ProjectsUnity/City/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 Ti (ID=0x2803)
    Vendor:   NVIDIA
    VRAM:     7949 MB
    Driver:   32.0.15.6117
Initialize mono
Mono path[0] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56252
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001551 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 67.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.393 seconds
Domain Reload Profiling:
	ReloadAssembly (393ms)
		BeginReloadAssembly (39ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (295ms)
			LoadAssemblies (39ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (61ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (17ms)
			SetupLoadedEditorAssemblies (188ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (32ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (67ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (59ms)
				ProcessInitializeOnLoadMethodAttributes (29ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.003279 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 64.93 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.888 seconds
Domain Reload Profiling:
	ReloadAssembly (888ms)
		BeginReloadAssembly (173ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (123ms)
		EndReloadAssembly (653ms)
			LoadAssemblies (50ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (166ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (45ms)
			SetupLoadedEditorAssemblies (352ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (65ms)
				BeforeProcessingInitializeOnLoad (70ms)
				ProcessInitializeOnLoadAttributes (154ms)
				ProcessInitializeOnLoadMethodAttributes (54ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 0.65 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5726 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (1.2 MB). Loaded Objects now: 6166.
Memory consumption went from 221.0 MB to 219.7 MB.
Total: 2.480700 ms (FindLiveObjects: 0.246200 ms CreateObjectMapping: 0.104800 ms MarkObjects: 1.812200 ms  DeleteObjects: 0.316600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002411 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.68 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.745 seconds
Domain Reload Profiling:
	ReloadAssembly (746ms)
		BeginReloadAssembly (77ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (26ms)
		EndReloadAssembly (617ms)
			LoadAssemblies (45ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (169ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (38ms)
			SetupLoadedEditorAssemblies (307ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (57ms)
				ProcessInitializeOnLoadAttributes (165ms)
				ProcessInitializeOnLoadMethodAttributes (67ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6169.
Memory consumption went from 213.7 MB to 212.4 MB.
Total: 2.432500 ms (FindLiveObjects: 0.224400 ms CreateObjectMapping: 0.082100 ms MarkObjects: 1.816400 ms  DeleteObjects: 0.309100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 19686.433925 seconds.
  path: Assets/_MyGame/Tilemap/PaletteSwap/PaletteSwap.unity
  artifactKey: Guid(9086626fcfcaac547aad8f26b0c3b998) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/PaletteSwap/PaletteSwap.unity using Guid(9086626fcfcaac547aad8f26b0c3b998) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '79056f9236afdd4ac2f8a610e27bace1') in 0.004932 seconds 
========================================================================
Received Import Request.
  Time since last request: 68.878855 seconds.
  path: Assets/_MyGame/Tilemap/PaletteSwap/Tile Palettes
  artifactKey: Guid(1eee44f0e3a7d064cbcadd12e13dd6a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/PaletteSwap/Tile Palettes using Guid(1eee44f0e3a7d064cbcadd12e13dd6a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6cf9158f1588912d19d50ad3f5a12127') in 0.000928 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.064611 seconds.
  path: Assets/_MyGame/Tilemap/PaletteSwap/Tile Palettes/Swap Palette A.prefab
  artifactKey: Guid(0105e23fd590cc54382a9b08fcada4ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/PaletteSwap/Tile Palettes/Swap Palette A.prefab using Guid(0105e23fd590cc54382a9b08fcada4ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd8f50f92ff63d6b89d7c2b93b1a0ec05') in 0.149641 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/_MyGame/Tilemap/PaletteSwap/Tile Palettes/Swap Palette C.prefab
  artifactKey: Guid(387b339880f70324db0d3c3e7ef82176) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/PaletteSwap/Tile Palettes/Swap Palette C.prefab using Guid(387b339880f70324db0d3c3e7ef82176) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '29a027ccd4bc2ef24bb88db45fe4de62') in 0.009671 seconds 
========================================================================
Received Import Request.
  Time since last request: 83.114969 seconds.
  path: Assets/_MyGame/Tilemap/PaletteSwap/Scripts/TilePaletteSwap.cs
  artifactKey: Guid(4aee667a2c990154f9018baf78fb16f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/PaletteSwap/Scripts/TilePaletteSwap.cs using Guid(4aee667a2c990154f9018baf78fb16f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '09701ab261eadd2d37f83766d2bd93b2') in 0.005477 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001944 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.715 seconds
Domain Reload Profiling:
	ReloadAssembly (716ms)
		BeginReloadAssembly (81ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (28ms)
		EndReloadAssembly (587ms)
			LoadAssemblies (45ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (154ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (301ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (63ms)
				ProcessInitializeOnLoadAttributes (163ms)
				ProcessInitializeOnLoadMethodAttributes (59ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.44 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5615 Unused Serialized files (Serialized files now loaded: 0)
Unloading 46 unused Assets / (1.2 MB). Loaded Objects now: 6244.
Memory consumption went from 220.9 MB to 219.7 MB.
Total: 3.371000 ms (FindLiveObjects: 0.275700 ms CreateObjectMapping: 0.193300 ms MarkObjects: 2.311800 ms  DeleteObjects: 0.589500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 96.223428 seconds.
  path: Assets/_MyGame/Tilemap/Normal Mapping/Scene
  artifactKey: Guid(afcb4abc96c3a7f4683e82f7912d4630) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Normal Mapping/Scene using Guid(afcb4abc96c3a7f4683e82f7912d4630) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '27c80861fba59048b61f8a1a42b227a9') in 0.004969 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.634751 seconds.
  path: Assets/_MyGame/Tilemap/Normal Mapping/Scene/normalmapping.unity
  artifactKey: Guid(c6fdaf4de1ca1aa40912830c91f720e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Normal Mapping/Scene/normalmapping.unity using Guid(c6fdaf4de1ca1aa40912830c91f720e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '135085366b9b40ff566ebdf66e5a3f50') in 0.000979 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001930 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.679 seconds
Domain Reload Profiling:
	ReloadAssembly (680ms)
		BeginReloadAssembly (75ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (25ms)
		EndReloadAssembly (553ms)
			LoadAssemblies (48ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (142ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (33ms)
			SetupLoadedEditorAssemblies (272ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (53ms)
				ProcessInitializeOnLoadAttributes (149ms)
				ProcessInitializeOnLoadMethodAttributes (54ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5616 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6247.
Memory consumption went from 221.0 MB to 219.7 MB.
Total: 2.457000 ms (FindLiveObjects: 0.260000 ms CreateObjectMapping: 0.078700 ms MarkObjects: 1.769300 ms  DeleteObjects: 0.348500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 51.438271 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Scenes
  artifactKey: Guid(ee73a6c16682c3048b5bcc719bfc3f8e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Scenes using Guid(ee73a6c16682c3048b5bcc719bfc3f8e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '41af8c07eeaa4990d2eae6f9c6454445') in 0.004489 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.672015 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Scenes/Hexagonal.unity
  artifactKey: Guid(6f619eb90e774944798989df285d3d01) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Scenes/Hexagonal.unity using Guid(6f619eb90e774944798989df285d3d01) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2ebb7c5c4e0c51e496fc2d3af34a9305') in 0.000767 seconds 
========================================================================
Received Import Request.
  Time since last request: 207.444297 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Land.asset
  artifactKey: Guid(976882b3510807d4fa556541b4e318c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Land.asset using Guid(976882b3510807d4fa556541b4e318c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '845fc7af9602cb89ab59a25300573061') in 0.036403 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.766008 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Sprites/phlosioneer_hex_coastline.png
  artifactKey: Guid(26700e8cdbc04874eb40c414c72c5400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Sprites/phlosioneer_hex_coastline.png using Guid(26700e8cdbc04874eb40c414c72c5400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c6fd7fe9d71fd65c5d8f3d8eba2b61c2') in 0.078258 seconds 
========================================================================
Received Import Request.
  Time since last request: 10.354167 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_0.asset
  artifactKey: Guid(bf7b78ee591c7c949805958a29bc46e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_0.asset using Guid(bf7b78ee591c7c949805958a29bc46e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '392876f6976beac85370edc107589399') in 0.004351 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.002298 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_12.asset
  artifactKey: Guid(7226ca900818f4f4db5950a8b869e6c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_12.asset using Guid(7226ca900818f4f4db5950a8b869e6c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bde7ebbffb2bf7ce4485e79d61ce8c09') in 0.004068 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000421 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_13.asset
  artifactKey: Guid(68f3bdd9165056d42a9d7df5013b8bdf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_13.asset using Guid(68f3bdd9165056d42a9d7df5013b8bdf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '652ee2716b255c1da73e7b005990b23b') in 0.003700 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.001728 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_16.asset
  artifactKey: Guid(591644c68b32a954ba64d291fceed9b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_16.asset using Guid(591644c68b32a954ba64d291fceed9b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '18efd77171616b5eb5bad35b29ee681d') in 0.003983 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.001880 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_25.asset
  artifactKey: Guid(adfaa88294ce73848bd0199f57401753) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_25.asset using Guid(adfaa88294ce73848bd0199f57401753) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8d872e117e548b31cde8b5b9f5467f1d') in 0.004535 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_33.asset
  artifactKey: Guid(9cddacc810f8f234e84580b194162513) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_33.asset using Guid(9cddacc810f8f234e84580b194162513) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1331bb3abb6b7655002241818941a68e') in 0.004216 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_40.asset
  artifactKey: Guid(578f8988ff48a0a4b9428dbefc6750a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_40.asset using Guid(578f8988ff48a0a4b9428dbefc6750a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0226d6d8f74b1734bcb60b6876edcb35') in 0.004327 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_74.asset
  artifactKey: Guid(c159d4b2d75bb4d4fb81e9fc2a02dab6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_74.asset using Guid(c159d4b2d75bb4d4fb81e9fc2a02dab6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a6dfbc4ce42981df434f6bb601fd6e79') in 0.003889 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_41.asset
  artifactKey: Guid(22600f759df7d5e42ada6642faed4305) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_41.asset using Guid(22600f759df7d5e42ada6642faed4305) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7159510d794f79d7b8698a6918c1fd99') in 0.004599 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_36.asset
  artifactKey: Guid(694e90108f730be4886288e47b08729d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_36.asset using Guid(694e90108f730be4886288e47b08729d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3913e950168548c99bab5cd3fa0b3ddc') in 0.004289 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_71.asset
  artifactKey: Guid(9484d82b552f2d44e82d58195d7fdad6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_71.asset using Guid(9484d82b552f2d44e82d58195d7fdad6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '413625b9e9767bb62ea7d481688768c7') in 0.004287 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_62.asset
  artifactKey: Guid(2e39300f2028e804d9d4417f8c024558) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_62.asset using Guid(2e39300f2028e804d9d4417f8c024558) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2fc93d03e166d2675805ffc5f59dc0c9') in 0.004167 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_32.asset
  artifactKey: Guid(17bca3a290431c646ae6f7f3c215ebb8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_32.asset using Guid(17bca3a290431c646ae6f7f3c215ebb8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '74819d4b302d87345e32f58cd56aa1d4') in 0.003786 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_44.asset
  artifactKey: Guid(8ee9efce82f7c34448f1d2231ab72627) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_44.asset using Guid(8ee9efce82f7c34448f1d2231ab72627) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1c54767bfb9487ba92f44f7b77325a6d') in 0.003743 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_53.asset
  artifactKey: Guid(6cb5a50090eade64697097d5b4cd3381) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_53.asset using Guid(6cb5a50090eade64697097d5b4cd3381) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '50e8ed6e7f914eebd284f152b69346df') in 0.004649 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_46.asset
  artifactKey: Guid(59c3dc7b6c6efd543a14b271fc551aac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_46.asset using Guid(59c3dc7b6c6efd543a14b271fc551aac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8e13482ea5028add378e9646808ddd25') in 0.004251 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_38.asset
  artifactKey: Guid(c9370c241b8f9244aa877ecb92812dc1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_38.asset using Guid(c9370c241b8f9244aa877ecb92812dc1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '20f79e25a3b7b8e91cbf193edfec7551') in 0.004081 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_47.asset
  artifactKey: Guid(7f4a47a691a94e84796e307d4929bb6a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_47.asset using Guid(7f4a47a691a94e84796e307d4929bb6a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'db66713aa054e7a1cbde021b59440844') in 0.003721 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_48.asset
  artifactKey: Guid(9dbc8277008481245871467aac0728b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_48.asset using Guid(9dbc8277008481245871467aac0728b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '87dc7d0a27a87124337ce45aab09bc7c') in 0.004280 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_60.asset
  artifactKey: Guid(5683591d3c29d6243af8b18e94ab0105) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_60.asset using Guid(5683591d3c29d6243af8b18e94ab0105) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd99564888f0e1d80e3abe2e751ce6714') in 0.004297 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_26.asset
  artifactKey: Guid(ab36663b0f559fb4a8ef6937e3505bf8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_26.asset using Guid(ab36663b0f559fb4a8ef6937e3505bf8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c89316b0fdb3d4ba70df7ceace09a0aa') in 0.003691 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_75.asset
  artifactKey: Guid(e2e8adc179ad25d4eaab18a4ff4dd6cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_75.asset using Guid(e2e8adc179ad25d4eaab18a4ff4dd6cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b1e018af0a7976ecc2f9250f2aa47309') in 0.003904 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_65.asset
  artifactKey: Guid(0d084d78fd0474c46bc8ab3002d66e37) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_65.asset using Guid(0d084d78fd0474c46bc8ab3002d66e37) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3d4fff32d532859a2f0c983b3aeac017') in 0.004103 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_64.asset
  artifactKey: Guid(e93a9f13f9218c44fb2a881c757a17ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_64.asset using Guid(e93a9f13f9218c44fb2a881c757a17ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '324e186acf8167971b2fd2ca6927ba82') in 0.004248 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_56.asset
  artifactKey: Guid(588f9674ba0ea604889c60153d482f56) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_56.asset using Guid(588f9674ba0ea604889c60153d482f56) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '00a733cd555bba790637c90f14c9ccac') in 0.005061 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_72.asset
  artifactKey: Guid(a39eaea71a3c3f147996d992e7be6f65) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_72.asset using Guid(a39eaea71a3c3f147996d992e7be6f65) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fbb0313f815d5cf4d4c73521ccdedb70') in 0.004367 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_70.asset
  artifactKey: Guid(81b76188c4d677349b4e52ec6ce38b72) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_70.asset using Guid(81b76188c4d677349b4e52ec6ce38b72) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7c9744f503b56d7217270a2dbdb1f4ac') in 0.004458 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_66.asset
  artifactKey: Guid(43e2c3aa20c8dcc41964a57f5317a14b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_66.asset using Guid(43e2c3aa20c8dcc41964a57f5317a14b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9457201b73faf3d21ebb514a58bb88b7') in 0.004348 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_11.asset
  artifactKey: Guid(8d144c4462363b941af88aa82bc55597) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_11.asset using Guid(8d144c4462363b941af88aa82bc55597) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7dc61a9d38859ae636bd9673ac546379') in 0.005529 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_22.asset
  artifactKey: Guid(b96fd7e8dcfb81f4ca179631927c92db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_22.asset using Guid(b96fd7e8dcfb81f4ca179631927c92db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1acc03a7645b62c7f56901d15315698f') in 0.003658 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_24.asset
  artifactKey: Guid(295ff450cd42e4c4bbfc1ed44901d898) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_24.asset using Guid(295ff450cd42e4c4bbfc1ed44901d898) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9794dc941fde3eafebb80fda3d7f3922') in 0.003547 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_57.asset
  artifactKey: Guid(09b7ca64206a2814499e4a2f37ae6cbf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_57.asset using Guid(09b7ca64206a2814499e4a2f37ae6cbf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'de27d67211571cf72c19ff458c212a6e') in 0.003773 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.001663 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_79.asset
  artifactKey: Guid(46f03dbbd94cf3149ab696e448642547) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_79.asset using Guid(46f03dbbd94cf3149ab696e448642547) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '007a9ca7193eee10f0b744ef3c0f5b46') in 0.004806 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_82.asset
  artifactKey: Guid(520251b743120a3489d995d850f83cf8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_82.asset using Guid(520251b743120a3489d995d850f83cf8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '96ca33fa1deaaaeef0f76d7f9fc1a123') in 0.004633 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0