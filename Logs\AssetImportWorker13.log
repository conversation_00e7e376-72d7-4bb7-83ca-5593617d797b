Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.14f1 (eee1884e7226) revision 15655304'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 32577 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files\Unity\2021.3.14f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker13
-projectPath
E:/ProjectsUnity/City
-logFile
Logs/AssetImportWorker13.log
-srvPort
61627
Successfully changed project path to: E:/ProjectsUnity/City
E:/ProjectsUnity/City
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Refreshing native plugins compatible for Editor in 69.33 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.14f1 (eee1884e7226)
[Subsystems] Discovering subsystems at path D:/Program Files/Unity/2021.3.14f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/ProjectsUnity/City/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 Ti (ID=0x2803)
    Vendor:   NVIDIA
    VRAM:     7949 MB
    Driver:   32.0.15.6117
Initialize mono
Mono path[0] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56260
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001477 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 75.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.434 seconds
Domain Reload Profiling:
	ReloadAssembly (435ms)
		BeginReloadAssembly (47ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (325ms)
			LoadAssemblies (47ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (70ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (20ms)
			SetupLoadedEditorAssemblies (203ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (35ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (76ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (61ms)
				ProcessInitializeOnLoadMethodAttributes (30ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.002104 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 76.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.931 seconds
Domain Reload Profiling:
	ReloadAssembly (932ms)
		BeginReloadAssembly (169ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (114ms)
		EndReloadAssembly (702ms)
			LoadAssemblies (51ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (177ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (47ms)
			SetupLoadedEditorAssemblies (391ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (8ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (76ms)
				BeforeProcessingInitializeOnLoad (77ms)
				ProcessInitializeOnLoadAttributes (168ms)
				ProcessInitializeOnLoadMethodAttributes (60ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Refreshing native plugins compatible for Editor in 1.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5727 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (1.2 MB). Loaded Objects now: 6169.
Memory consumption went from 221.4 MB to 220.2 MB.
Total: 4.166000 ms (FindLiveObjects: 0.377400 ms CreateObjectMapping: 0.219100 ms MarkObjects: 3.092300 ms  DeleteObjects: 0.476000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 26656.018585 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Run W.anim
  artifactKey: Guid(495813948220d46c9b3ac51fe6fa061b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Run W.anim using Guid(495813948220d46c9b3ac51fe6fa061b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '6f6bcd2c96093124c6db28705dd9e68f') in 0.158866 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Terrain Tile/Tile Palettes/Terrain Tiles.prefab
  artifactKey: Guid(eb2d7c8763eb02a4aa672ee23bb44c52) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Tiles/Terrain Tile/Tile Palettes/Terrain Tiles.prefab using Guid(eb2d7c8763eb02a4aa672ee23bb44c52) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'ed6539be5dd9afdfa5d626aa0aba35bb') in 0.017397 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/DockConsole.prefab
  artifactKey: Guid(e574103baba252a409527f51a20b3a29) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/DockConsole.prefab using Guid(e574103baba252a409527f51a20b3a29) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '0812845b5bb6cc0a8b2e6b93cd534a56') in 0.003263 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Platform Tile 2/Sprites/platform2.png
  artifactKey: Guid(b206d8d9f3ab87f4aa256b0df86a7f29) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Platform Tile 2/Sprites/platform2.png using Guid(b206d8d9f3ab87f4aa256b0df86a7f29) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '3e1550af088ecbe7dc80601db43ddb3b') in 0.055273 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Dungeon Tile/Tile Asset/Cave.asset
  artifactKey: Guid(b2f84e66a5337c541ad85c2b6fac913e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Dungeon Tile/Tile Asset/Cave.asset using Guid(b2f84e66a5337c541ad85c2b6fac913e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '9a9da0c0c028063cd3f66a632c7052e3') in 0.009228 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_77.asset
  artifactKey: Guid(dad24a8e78809c74c8ebff5b2c61d039) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_77.asset using Guid(dad24a8e78809c74c8ebff5b2c61d039) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'ec3b9b412c74f58566aa6a3971cbe239') in 0.007126 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Static NW.anim
  artifactKey: Guid(448eab6070ad246c1b6bbed5dc5d3059) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Static NW.anim using Guid(448eab6070ad246c1b6bbed5dc5d3059) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'f53a583f3e042cb126db1f42e33dfbdb') in 0.005025 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run S/witch run S01.png
  artifactKey: Guid(817f3932bf0d947868b7f3a300bc83bb) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run S/witch run S01.png using Guid(817f3932bf0d947868b7f3a300bc83bb) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '818b1e5b0ee357fe152481dc204296f6') in 0.011432 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Tabs/Options.prefab
  artifactKey: Guid(f38fe2ab9831b6c4c9c90aef8dabcb69) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Tabs/Options.prefab using Guid(f38fe2ab9831b6c4c9c90aef8dabcb69) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '67c094b743f72d996bdd23264d9a9477') in 0.022667 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Static S.anim
  artifactKey: Guid(9848f1025f7c545cd96f231c5185e53a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Static S.anim using Guid(9848f1025f7c545cd96f231c5185e53a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'fb9c485a0e516c30beab04b4456bb1ca') in 0.003911 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Options/NumberOption.prefab
  artifactKey: Guid(2ac1e31ed7a70544795db2044eb915e1) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Options/NumberOption.prefab using Guid(2ac1e31ed7a70544795db2044eb915e1) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '3d9899d7b138d4d7dbb9b13925bd80fb') in 0.015186 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run E/witch run E01.png
  artifactKey: Guid(c1a8839b406914083af460fe590233d7) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run E/witch run E01.png using Guid(c1a8839b406914083af460fe590233d7) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'be6801f86f6235ec505739e34bbc6146') in 0.011780 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run N/witch run N00.png
  artifactKey: Guid(5927a47e43c3a495ab2f4736eb3c0312) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run N/witch run N00.png using Guid(5927a47e43c3a495ab2f4736eb3c0312) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'd80641925cd80719be6ac4c1c15fcd63') in 0.012103 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run NE/witch run NE04.png
  artifactKey: Guid(ece1112da122147a783247177dc5c414) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run NE/witch run NE04.png using Guid(ece1112da122147a783247177dc5c414) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '81f48542e202bf9aece56d8d7be3cd02') in 0.011968 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/pin-50.png
  artifactKey: Guid(23dfb5d7fc5a2fb4f9a5fc00f697147f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/pin-50.png using Guid(23dfb5d7fc5a2fb4f9a5fc00f697147f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '9a6c3edf4c4270bb2dd1ef3617442446') in 0.014203 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Animated Tile/Sprites/Animated Tiles.png
  artifactKey: Guid(bf541ba7810cafd43af665d35d1dc29e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Tiles/Animated Tile/Sprites/Animated Tiles.png using Guid(bf541ba7810cafd43af665d35d1dc29e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '8c428a452306c9792a0a7e033f7da014') in 0.028552 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/info-64.png
  artifactKey: Guid(4ea5932536416924bab8eb9b5bab3874) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/info-64.png using Guid(4ea5932536416924bab8eb9b5bab3874) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '467cbeff4271c512792f405749d1234e') in 0.011404 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/StompyRobot/SRDebugger/Scripts/UI/Other/IEnableTab.cs
  artifactKey: Guid(a0522b3ec2155a4468bea956e3ea2fd5) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Scripts/UI/Other/IEnableTab.cs using Guid(a0522b3ec2155a4468bea956e3ea2fd5) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '58ab3f05a3d2901e21aa319e82dfe98c') in 0.006990 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Custom Rule Tile/Custom Rule Tile.unity
  artifactKey: Guid(3c2b729a1939ea14b8474f18e6dbd07f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Custom Rule Tile/Custom Rule Tile.unity using Guid(3c2b729a1939ea14b8474f18e6dbd07f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '804eaa813961a2a83850ebf38a10ff7d') in 0.002351 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/StompyRobot/SRDebugger/Editor/Icons/Dark/profiler-25.png
  artifactKey: Guid(a65d8899e293fc449bc3d57710810333) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Editor/Icons/Dark/profiler-25.png using Guid(a65d8899e293fc449bc3d57710810333) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '50596412a049f87773b5544b9fc41198') in 0.009423 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/StompyRobot/SRDebugger/Scripts/Services/Implementation/SRDebugService.cs
  artifactKey: Guid(aa53ffe51d3a35545b9277a06967b48a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Scripts/Services/Implementation/SRDebugService.cs using Guid(aa53ffe51d3a35545b9277a06967b48a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '0aad0fd45cc75075567013c24366eefb') in 0.002611 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Random Tile/Tile Assets/RandomTempleGrassTile.asset
  artifactKey: Guid(36f6dda10e473614da03dbc9b8a9775c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Tiles/Random Tile/Tile Assets/RandomTempleGrassTile.asset using Guid(36f6dda10e473614da03dbc9b8a9775c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '311def537a8ebbffe9d60b3cc83625ed') in 0.002732 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Run NW.anim
  artifactKey: Guid(e994ba0a7d2244dd193bc93033c2889c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Run NW.anim using Guid(e994ba0a7d2244dd193bc93033c2889c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '553fa79a8ac9697d06963d90e539ab10') in 0.003918 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/StompyRobot/SRDebugger/Scripts/Services/Implementation/BugReportApiService.cs
  artifactKey: Guid(0f0a70f9ea64595459ec202791f9954a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Scripts/Services/Implementation/BugReportApiService.cs using Guid(0f0a70f9ea64595459ec202791f9954a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'd4d1225e39811bb71ebcebbe999106ae') in 0.003888 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Scripts/CustomAxisSortCamera.cs
  artifactKey: Guid(628cdf88c48134e4c9094a92ad1bb52e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Scripts/CustomAxisSortCamera.cs using Guid(628cdf88c48134e4c9094a92ad1bb52e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '19d18b448112fa62a41e81d9aa178e7c') in 0.002380 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Options/BoolOption.prefab
  artifactKey: Guid(c00f61f0f69828745b7ef9145737e260) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Options/BoolOption.prefab using Guid(c00f61f0f69828745b7ef9145737e260) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '6bf3c91d07f049450fdd25fccd41866b') in 0.018242 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_64.asset
  artifactKey: Guid(e93a9f13f9218c44fb2a881c757a17ce) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_64.asset using Guid(e93a9f13f9218c44fb2a881c757a17ce) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '2a0afc4395808e6e51289737798fcfae') in 0.004748 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Weighted Random Tile/Tile Assets/WeightedRandomTempleGrassTile.asset
  artifactKey: Guid(7c260df917165064e858a93cf138d9e0) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Tiles/Weighted Random Tile/Tile Assets/WeightedRandomTempleGrassTile.asset using Guid(7c260df917165064e858a93cf138d9e0) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'f5bce1198ccd25741e3602c73dd7fff3') in 0.002366 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/open-dropdown-50.png
  artifactKey: Guid(3174499e448de9d49840e98d8913aa07) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/open-dropdown-50.png using Guid(3174499e448de9d49840e98d8913aa07) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '9f5d54edd12c1f383dfe2aa6380889eb') in 0.011959 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Options/ActionOption.prefab
  artifactKey: Guid(308ca70361d90ed40bcc5c69d973f7ae) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Options/ActionOption.prefab using Guid(308ca70361d90ed40bcc5c69d973f7ae) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '61fd78cdc7253806ad56c7f5b5f2cdf5') in 0.011477 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Scenes/Hexagonal.unity
  artifactKey: Guid(6f619eb90e774944798989df285d3d01) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Scenes/Hexagonal.unity using Guid(6f619eb90e774944798989df285d3d01) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'c9bed96e6dd107869bfe923dae890b24') in 0.002783 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Terrain Tile/Tile Assets/Pebbles.asset
  artifactKey: Guid(84f5a8cb641d46b4da008e78abe50772) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Tiles/Terrain Tile/Tile Assets/Pebbles.asset using Guid(84f5a8cb641d46b4da008e78abe50772) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'ced29a1182b108b5478b8eebb06f8125') in 0.002585 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/heart_monitor-64.png
  artifactKey: Guid(b5b46026463fb334bb8a3b95b303b224) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/heart_monitor-64.png using Guid(b5b46026463fb334bb8a3b95b303b224) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '7d4829ab63ad5ad1232193342fdd3d58') in 0.017566 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Ladder Tile/Tile Asset/Ladder.asset
  artifactKey: Guid(1b0092e389db5b6479f94c86104b9d12) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Ladder Tile/Tile Asset/Ladder.asset using Guid(1b0092e389db5b6479f94c86104b9d12) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'd2b1b68f68b5675f82216ca392e59392') in 0.003969 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/StompyRobot/SRDebugger/Animations/UI/Pin/Result.controller
  artifactKey: Guid(bed23c49fcd255d489e4d56fc45840c5) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Animations/UI/Pin/Result.controller using Guid(bed23c49fcd255d489e4d56fc45840c5) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '5d410863154728d0bbd25bd2c20ad602') in 0.005133 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_38.asset
  artifactKey: Guid(c9370c241b8f9244aa877ecb92812dc1) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_38.asset using Guid(c9370c241b8f9244aa877ecb92812dc1) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'a8ffa9646316c1614d3d00ccdba4c4f8') in 0.004230 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/BugReportPopover.prefab
  artifactKey: Guid(b1e14a36132e86c4a86149b6ecbe53f4) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/BugReportPopover.prefab using Guid(b1e14a36132e86c4a86149b6ecbe53f4) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'df8e966d6b177ed2dfc65454d12195f9') in 0.017085 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_41.asset
  artifactKey: Guid(22600f759df7d5e42ada6642faed4305) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_41.asset using Guid(22600f759df7d5e42ada6642faed4305) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'da643d3da06eeb9ea565b7c693da9ced') in 0.004465 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/StompyRobot/SRDebugger/Scripts/UI/Other/HandleManager.cs
  artifactKey: Guid(4292e93ad30fda64b96c60d4d68b3c0d) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Scripts/UI/Other/HandleManager.cs using Guid(4292e93ad30fda64b96c60d4d68b3c0d) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '2b9d05c7b5fcc6dae2e4ff6393aab08e') in 0.002673 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_16.asset
  artifactKey: Guid(591644c68b32a954ba64d291fceed9b9) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_16.asset using Guid(591644c68b32a954ba64d291fceed9b9) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '635ff2ecb54e5d7e7279a3c7a4f8aece') in 0.004356 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/StompyRobot/SRF/Scripts/UI/ResponsiveEnable.cs
  artifactKey: Guid(2415a15c0c9eea041863fc6c1a434ede) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRF/Scripts/UI/ResponsiveEnable.cs using Guid(2415a15c0c9eea041863fc6c1a434ede) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '26e905c66fe5086c19e1b1262d672903') in 0.003472 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/StompyRobot/SRDebugger/UI/Sprites/Default/<EMAIL>
  artifactKey: Guid(a0c7ffb7676bd694192d1a9b3f8d20bd) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/UI/Sprites/Default/<EMAIL> using Guid(a0c7ffb7676bd694192d1a9b3f8d20bd) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '6065b83dd674b1776468ead47c3bf084') in 0.019748 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.546119 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run SE/witch run SE03.png
  artifactKey: Guid(1f80fbf0493574a95ae512ab1cc9d7d6) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run SE/witch run SE03.png using Guid(1f80fbf0493574a95ae512ab1cc9d7d6) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '21331a59f2f26f2d6353c4535ff711cc') in 0.013289 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_04.png
  artifactKey: Guid(f427ccb89e8fdb745b8f47ea8b3d9d0c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_04.png using Guid(f427ccb89e8fdb745b8f47ea8b3d9d0c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '93eb0376ad09123917f5b08617a2718c') in 0.012577 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static07.png
  artifactKey: Guid(92d40b9c71eda4dfd83985e7fdc8572e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static07.png using Guid(92d40b9c71eda4dfd83985e7fdc8572e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '5ed0fd1fd71206745a5bc4128d1d544a') in 0.011342 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static02.png
  artifactKey: Guid(c94b2ada7d068424d9b4c20219f862fe) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static02.png using Guid(c94b2ada7d068424d9b4c20219f862fe) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '4c93d5c953383fef530a3e8aeca541ae') in 0.013030 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_30.png
  artifactKey: Guid(2eac3474fe5c2714788e1ecece163387) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_30.png using Guid(2eac3474fe5c2714788e1ecece163387) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'ec2d2be6e870a1e9b2da445b373fa715') in 0.011579 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static00.png
  artifactKey: Guid(1ab8c3bef1c8d41c2bcaeb963b25c84f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static00.png using Guid(1ab8c3bef1c8d41c2bcaeb963b25c84f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '96e3708a79b68496313bbceb0fa003cc') in 0.013614 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/RuleTiles/NeighbourTiles/NeighbourTile_Desert_RaisedSand.asset
  artifactKey: Guid(cdb7575da9d10f346ad8b0a600400c28) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/RuleTiles/NeighbourTiles/NeighbourTile_Desert_RaisedSand.asset using Guid(cdb7575da9d10f346ad8b0a600400c28) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '9d5b6c7c021d486fad917fdcabe8df32') in 0.005511 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_60.png
  artifactKey: Guid(c64f4950b5299954aba95a6400bfba96) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_60.png using Guid(c64f4950b5299954aba95a6400bfba96) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '55a092949921faca7e2f0773648f2f0e') in 0.009664 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_64.png
  artifactKey: Guid(f77a8439147b8f042ab1d13d5d78e902) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_64.png using Guid(f77a8439147b8f042ab1d13d5d78e902) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '84651d0df1a13d86502515d875016e73') in 0.011472 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Colliders/ColliderTiles/tile-border-left.asset
  artifactKey: Guid(7e12fb73967d34777ba76171e8be7b04) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Colliders/ColliderTiles/tile-border-left.asset using Guid(7e12fb73967d34777ba76171e8be7b04) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'b08f108c2e61b7bcee93646295eefa7f') in 0.004143 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static06.png
  artifactKey: Guid(0a91f6ffdd6c74fc2bbe0ca9bcd3b52d) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static06.png using Guid(0a91f6ffdd6c74fc2bbe0ca9bcd3b52d) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '6374e59198603df8ec5118e12b435b50') in 0.011353 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_31.png
  artifactKey: Guid(29ac072f835dc6945b5e1d0aa71dcc03) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_31.png using Guid(29ac072f835dc6945b5e1d0aa71dcc03) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'c1965b4949ee1e7d63aff681f7b87ed2') in 0.012000 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_25.png
  artifactKey: Guid(4bada23310046ca4bb98f9d8bdf17a15) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_25.png using Guid(4bada23310046ca4bb98f9d8bdf17a15) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '64eab1f9a7719b00722e3ccf402ab797') in 0.010768 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0