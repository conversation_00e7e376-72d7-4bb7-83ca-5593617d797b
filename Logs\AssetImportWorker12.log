Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.14f1 (eee1884e7226) revision 15655304'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 32577 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files\Unity\2021.3.14f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker12
-projectPath
E:/ProjectsUnity/City
-logFile
Logs/AssetImportWorker12.log
-srvPort
61627
Successfully changed project path to: E:/ProjectsUnity/City
E:/ProjectsUnity/City
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Refreshing native plugins compatible for Editor in 71.35 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.14f1 (eee1884e7226)
[Subsystems] Discovering subsystems at path D:/Program Files/Unity/2021.3.14f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/ProjectsUnity/City/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 Ti (ID=0x2803)
    Vendor:   NVIDIA
    VRAM:     7949 MB
    Driver:   32.0.15.6117
Initialize mono
Mono path[0] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56832
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001435 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 70.88 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.420 seconds
Domain Reload Profiling:
	ReloadAssembly (420ms)
		BeginReloadAssembly (41ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (317ms)
			LoadAssemblies (40ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (67ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (20ms)
			SetupLoadedEditorAssemblies (200ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (36ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (71ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (62ms)
				ProcessInitializeOnLoadMethodAttributes (30ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.002925 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 79.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.940 seconds
Domain Reload Profiling:
	ReloadAssembly (941ms)
		BeginReloadAssembly (166ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (117ms)
		EndReloadAssembly (712ms)
			LoadAssemblies (49ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (174ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (50ms)
			SetupLoadedEditorAssemblies (401ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (79ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (171ms)
				ProcessInitializeOnLoadMethodAttributes (65ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (4ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Refreshing native plugins compatible for Editor in 1.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5727 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (1.2 MB). Loaded Objects now: 6169.
Memory consumption went from 221.4 MB to 220.2 MB.
Total: 3.640600 ms (FindLiveObjects: 0.318100 ms CreateObjectMapping: 0.113900 ms MarkObjects: 2.699700 ms  DeleteObjects: 0.507700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 26656.025241 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Custom Rule Tile/Sprites/dungeonblue.png
  artifactKey: Guid(1323b38fd2229a441a036c00a0c74fa7) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Custom Rule Tile/Sprites/dungeonblue.png using Guid(1323b38fd2229a441a036c00a0c74fa7) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '5850cfbcf2296efc50729932a26ddc9f') in 0.210565 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/refresh-50.png
  artifactKey: Guid(9662640d4260c9a4d926c7de03e6609e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/refresh-50.png using Guid(9662640d4260c9a4d926c7de03e6609e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '68014c9c361fb63f4c23f5b0fc462f49') in 0.011404 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/StompyRobot/SRDebugger/Scripts/UI/Controls/Data/ReadOnlyControl.cs
  artifactKey: Guid(41b5dbc85a74ae84dbb6c6685e1151fc) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Scripts/UI/Controls/Data/ReadOnlyControl.cs using Guid(41b5dbc85a74ae84dbb6c6685e1151fc) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '920e13187a1b2575d77cbc21db990585') in 0.012640 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Foliage Tile/Sprites/foliage.png
  artifactKey: Guid(978047fa06043f24db6c15d7cd535997) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Foliage Tile/Sprites/foliage.png using Guid(978047fa06043f24db6c15d7cd535997) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '8246cf20d79de896936c8981e7fceb61') in 0.024961 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run S/witch run S02.png
  artifactKey: Guid(bcd509b8209dc4a3fa01e45e0b8cccda) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run S/witch run S02.png using Guid(bcd509b8209dc4a3fa01e45e0b8cccda) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'f1d40ca6b6a7a07a9f3dea600464b786') in 0.013641 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run S/witch run S05.png
  artifactKey: Guid(1a2cbf2c652bd40018e61a2f02e75c5a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run S/witch run S05.png using Guid(1a2cbf2c652bd40018e61a2f02e75c5a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '375924b577819147ef8f0bf3c00bfbf8') in 0.012304 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run NE/witch run NE01.png
  artifactKey: Guid(4c0cff268c40d40cc95641209b04439e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run NE/witch run NE01.png using Guid(4c0cff268c40d40cc95641209b04439e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '78dbcdcbfc3602af39ba567ae1e90cb7') in 0.014974 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Static SW.anim
  artifactKey: Guid(4b0c786369feb49bb913558f72dcaa18) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Static SW.anim using Guid(4b0c786369feb49bb913558f72dcaa18) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '4790aa86386024e8c00b86394c224169') in 0.007446 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Prefabs/Decorations/Desert/Bones_3.prefab
  artifactKey: Guid(ded228c73cadcf445b1ac399f54c2a97) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Prefabs/Decorations/Desert/Bones_3.prefab using Guid(ded228c73cadcf445b1ac399f54c2a97) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '67932099b1e6921a4d982b8e474a3338') in 0.009385 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run NE/witch run NE05.png
  artifactKey: Guid(6a6e4819cef514a4d99d81c5b1f52f85) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run NE/witch run NE05.png using Guid(6a6e4819cef514a4d99d81c5b1f52f85) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'd90e07901a1db7d77e21d0c4a002162a') in 0.011031 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Prefabs/Decorations/Desert/Tree_Palm.prefab
  artifactKey: Guid(3b0788909d2477247a43cb4f4ceb765d) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Prefabs/Decorations/Desert/Tree_Palm.prefab using Guid(3b0788909d2477247a43cb4f4ceb765d) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'ed62fcabf31b4960508d6735a135c9c6') in 0.011317 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Static NE.anim
  artifactKey: Guid(b11eb7d29cbea4b99b679dedf0796faa) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Static NE.anim using Guid(b11eb7d29cbea4b99b679dedf0796faa) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '337161f3ca855b5eb2abbb19517473ee') in 0.003772 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Animated Tile/Tile Asset/WaterfallSplash.asset
  artifactKey: Guid(1a3c9caf04e037745a6f6529edf20344) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Tiles/Animated Tile/Tile Asset/WaterfallSplash.asset using Guid(1a3c9caf04e037745a6f6529edf20344) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '8bf2cab0aec28abd29521041ca261e7f') in 0.010842 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_52.asset
  artifactKey: Guid(54ea36bde4a235646a62e2949556e387) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_52.asset using Guid(54ea36bde4a235646a62e2949556e387) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '33b64a7136a80da5911c95a98cc64b51') in 0.005449 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Static E.anim
  artifactKey: Guid(41b0b1240be85442483ecb8ad35c0c09) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Static E.anim using Guid(41b0b1240be85442483ecb8ad35c0c09) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'ff614b208a01e7a6887f21af2de9db35') in 0.004551 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Animated Tile/Tile Palette/Animated Tiles.prefab
  artifactKey: Guid(e48163b530eeebc49b82a236bd09123c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Tiles/Animated Tile/Tile Palette/Animated Tiles.prefab using Guid(e48163b530eeebc49b82a236bd09123c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '5b24ed039219596e8e109bae398a0163') in 0.007066 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Pipeline Tile/Sprites/pipeline.png
  artifactKey: Guid(e10f947be2bea7448b7f050392a49bfd) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Pipeline Tile/Sprites/pipeline.png using Guid(e10f947be2bea7448b7f050392a49bfd) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '595fa17d79d77366319e0876094b05fb') in 0.019647 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_27.asset
  artifactKey: Guid(30f98488c97193a4297e833e71136f5a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_27.asset using Guid(30f98488c97193a4297e833e71136f5a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '55076c247978e6488b60acecc1e501a5') in 0.004907 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_22.asset
  artifactKey: Guid(b96fd7e8dcfb81f4ca179631927c92db) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_22.asset using Guid(b96fd7e8dcfb81f4ca179631927c92db) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'b0255e248930c991020b89a03ccd254b') in 0.006865 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/Destructible/Sprites/destruct.PNG
  artifactKey: Guid(6effb0022735ead45acf361ef8832074) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Destructible/Sprites/destruct.PNG using Guid(6effb0022735ead45acf361ef8832074) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'e1f82f2634883a4417cb7f783c7c8022') in 0.149095 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_70.asset
  artifactKey: Guid(81b76188c4d677349b4e52ec6ce38b72) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_70.asset using Guid(81b76188c4d677349b4e52ec6ce38b72) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'bb82181cf31d4c6b28786f98dc13058d') in 0.004621 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/StompyRobot/SRDebugger/Scripts/UI/Other/BugReportSheetController.cs
  artifactKey: Guid(63d76b94b1c670b4cbafd57dd8dcd2ff) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Scripts/UI/Other/BugReportSheetController.cs using Guid(63d76b94b1c670b4cbafd57dd8dcd2ff) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'abd63839ac488c42f6ae6d94e0443a21') in 0.003752 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Pipeline Tile/Tile Asset/Pipeline.asset
  artifactKey: Guid(df0e6a3984206a54090c327c837f2bbf) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Pipeline Tile/Tile Asset/Pipeline.asset using Guid(df0e6a3984206a54090c327c837f2bbf) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'de27218e918c877a9a61eb00e6eb3d00') in 0.006627 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/pin-64.png
  artifactKey: Guid(d74ece6f102dfaa47aa02f6af42f0073) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/pin-64.png using Guid(d74ece6f102dfaa47aa02f6af42f0073) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'd0fa43dc79aeeefe5099b418f4aac267') in 0.011163 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_26.asset
  artifactKey: Guid(ab36663b0f559fb4a8ef6937e3505bf8) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_26.asset using Guid(ab36663b0f559fb4a8ef6937e3505bf8) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '9de0c2d6692c122d2061c047c78da5a5') in 0.004241 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/StompyRobot/SRF/Scripts/UI/Editor/LongPressButtonEditor.cs
  artifactKey: Guid(1d57f229ac662764aa4508a0d42ddede) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRF/Scripts/UI/Editor/LongPressButtonEditor.cs using Guid(1d57f229ac662764aa4508a0d42ddede) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '9889538fa2fcb0cb2cdf35abdfc73947') in 0.004152 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_68.asset
  artifactKey: Guid(392d7d8cda3be2941bb1180eaa57cba4) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_68.asset using Guid(392d7d8cda3be2941bb1180eaa57cba4) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '8f18219a7d532cc9c298553e785214e6') in 0.004396 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/StompyRobot/SRF/Scripts/UI/LongPressButton.cs
  artifactKey: Guid(3fc6d8b69639fdc45a849df5b853c783) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRF/Scripts/UI/LongPressButton.cs using Guid(3fc6d8b69639fdc45a849df5b853c783) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '2f62606fbe79398fbad1c7b6e63060c6') in 0.002925 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/StompyRobot/SRDebugger/Scripts/UI/Other/DockConsoleRoot.cs
  artifactKey: Guid(e502669a31931b74ea762540a783f1ff) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Scripts/UI/Other/DockConsoleRoot.cs using Guid(e502669a31931b74ea762540a783f1ff) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '3ff5e12dcc7cf18c4a83432a84e9c455') in 0.002400 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Custom Rule Tile/Tile Asset/grass.asset
  artifactKey: Guid(5e9bd9dfc1e43e74bb30f0e5ac0f1b0a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Custom Rule Tile/Tile Asset/grass.asset using Guid(5e9bd9dfc1e43e74bb30f0e5ac0f1b0a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'e9ff82f7d89bf899605dfe839391b48c') in 0.004336 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_39.asset
  artifactKey: Guid(9d7ebaa5bbd4158428750568934cfcf6) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_39.asset using Guid(9d7ebaa5bbd4158428750568934cfcf6) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'b183e217a3ce8e18b754c1c4b9d472a3') in 0.004363 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.563900 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run SE/witch run SE02.png
  artifactKey: Guid(f362e20b723ac4c87af16d0953dd902c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run SE/witch run SE02.png using Guid(f362e20b723ac4c87af16d0953dd902c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '771d2f06d766f447d19149b8d59619d3') in 0.012739 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static05.png
  artifactKey: Guid(da137afd2aa944addb4d15be30f7c6f5) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static05.png using Guid(da137afd2aa944addb4d15be30f7c6f5) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '1fde0eeeb000e20ad4b9655a6c689755') in 0.016822 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_29.png
  artifactKey: Guid(be4b70f86d38cbf41b09740e793d03a0) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_29.png using Guid(be4b70f86d38cbf41b09740e793d03a0) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '28456b4aad6cefba8d8041542140dd9b') in 0.010756 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_55.png
  artifactKey: Guid(ed50c1ae4775eea499022f215889061f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_55.png using Guid(ed50c1ae4775eea499022f215889061f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '0acf90245a6751ed29d177414c16ee1d') in 0.011631 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_13.png
  artifactKey: Guid(1fa9a4a9631823f44b33f6ac73e6e1d3) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_13.png using Guid(1fa9a4a9631823f44b33f6ac73e6e1d3) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'a3bf79f4ef9faf07527856e1031cc596') in 0.014649 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_32.png
  artifactKey: Guid(338493abd75ba2c48b8cb98205165bab) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_32.png using Guid(338493abd75ba2c48b8cb98205165bab) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'c8bb078db67ae62a7837f28a1aae696b') in 0.013456 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_06.png
  artifactKey: Guid(8af0c5a1b4b46cd4e908b6e919853f60) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_06.png using Guid(8af0c5a1b4b46cd4e908b6e919853f60) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'c725c5684a0a4ef8b3fa614a8b348623') in 0.011082 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/RuleTiles/NeighbourTiles/NeighbourTile_Desert_FlatSand.asset
  artifactKey: Guid(3152ed6d0cc59d54fa8ee0ea59412829) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/RuleTiles/NeighbourTiles/NeighbourTile_Desert_FlatSand.asset using Guid(3152ed6d0cc59d54fa8ee0ea59412829) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '2cf538f4d862364cd3dd2f751749290d') in 0.005504 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_62.png
  artifactKey: Guid(bafd22bb916594742a7c301816c24d94) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_62.png using Guid(bafd22bb916594742a7c301816c24d94) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'c42d219615a657c64a1c4437d0352379') in 0.011654 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_12.png
  artifactKey: Guid(60919206ec62ae74e92382a275b3e531) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_12.png using Guid(60919206ec62ae74e92382a275b3e531) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'bc3bfd75f78d570644e0fcdc0003961c') in 0.010734 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_53.png
  artifactKey: Guid(dbfe26f852ac8d046afaba7b0663c9b0) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_53.png using Guid(dbfe26f852ac8d046afaba7b0663c9b0) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '9806d7f7d80c6ebf6c8f6c06867af516') in 0.014723 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_40.png
  artifactKey: Guid(9c68dc1b6ad638c4daad0b20385272c3) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_40.png using Guid(9c68dc1b6ad638c4daad0b20385272c3) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '025fc2b59a0999a58f6162b69dfcad30') in 0.011158 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_02.png
  artifactKey: Guid(40c74bdb314e60147be4706b27817189) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_02.png using Guid(40c74bdb314e60147be4706b27817189) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '027550fe01a006b91faf230117db1d6f') in 0.011908 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0