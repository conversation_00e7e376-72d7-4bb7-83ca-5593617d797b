Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.14f1 (eee1884e7226) revision 15655304'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 32577 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files\Unity\2021.3.14f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
E:/ProjectsUnity/City
-logFile
Logs/AssetImportWorker2.log
-srvPort
61627
Successfully changed project path to: E:/ProjectsUnity/City
E:/ProjectsUnity/City
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Refreshing native plugins compatible for Editor in 73.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.14f1 (eee1884e7226)
[Subsystems] Discovering subsystems at path D:/Program Files/Unity/2021.3.14f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/ProjectsUnity/City/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 Ti (ID=0x2803)
    Vendor:   NVIDIA
    VRAM:     7949 MB
    Driver:   32.0.15.6117
Initialize mono
Mono path[0] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56176
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001450 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 73.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.487 seconds
Domain Reload Profiling:
	ReloadAssembly (487ms)
		BeginReloadAssembly (56ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (353ms)
			LoadAssemblies (57ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (84ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (18ms)
			SetupLoadedEditorAssemblies (217ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (37ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (73ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (69ms)
				ProcessInitializeOnLoadMethodAttributes (36ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.002555 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 76.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.085 seconds
Domain Reload Profiling:
	ReloadAssembly (1086ms)
		BeginReloadAssembly (172ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (124ms)
		EndReloadAssembly (843ms)
			LoadAssemblies (70ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (256ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (51ms)
			SetupLoadedEditorAssemblies (423ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (9ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (77ms)
				BeforeProcessingInitializeOnLoad (77ms)
				ProcessInitializeOnLoadAttributes (193ms)
				ProcessInitializeOnLoadMethodAttributes (66ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Refreshing native plugins compatible for Editor in 0.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5726 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (1.2 MB). Loaded Objects now: 6167.
Memory consumption went from 221.1 MB to 219.9 MB.
Total: 4.405600 ms (FindLiveObjects: 0.423800 ms CreateObjectMapping: 0.174300 ms MarkObjects: 3.308200 ms  DeleteObjects: 0.498500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002450 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.702 seconds
Domain Reload Profiling:
	ReloadAssembly (702ms)
		BeginReloadAssembly (75ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (577ms)
			LoadAssemblies (44ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (152ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (303ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (70ms)
				ProcessInitializeOnLoadAttributes (159ms)
				ProcessInitializeOnLoadMethodAttributes (57ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6170.
Memory consumption went from 213.6 MB to 212.4 MB.
Total: 3.953300 ms (FindLiveObjects: 0.342900 ms CreateObjectMapping: 0.201900 ms MarkObjects: 2.837200 ms  DeleteObjects: 0.570200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001837 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.67 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.665 seconds
Domain Reload Profiling:
	ReloadAssembly (666ms)
		BeginReloadAssembly (76ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (26ms)
		EndReloadAssembly (536ms)
			LoadAssemblies (43ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (147ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (264ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (51ms)
				ProcessInitializeOnLoadAttributes (144ms)
				ProcessInitializeOnLoadMethodAttributes (54ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6173.
Memory consumption went from 213.7 MB to 212.4 MB.
Total: 3.608400 ms (FindLiveObjects: 0.338600 ms CreateObjectMapping: 0.162400 ms MarkObjects: 2.627900 ms  DeleteObjects: 0.478600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 3746.429937 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static00.png
  artifactKey: Guid(1ab8c3bef1c8d41c2bcaeb963b25c84f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static00.png using Guid(1ab8c3bef1c8d41c2bcaeb963b25c84f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'def5764326ddbe2189dae285080e0568') in 0.068734 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static06.png
  artifactKey: Guid(0a91f6ffdd6c74fc2bbe0ca9bcd3b52d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static06.png using Guid(0a91f6ffdd6c74fc2bbe0ca9bcd3b52d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '513879cbb8422d37236c6c277600f809') in 0.005362 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static02.png
  artifactKey: Guid(c94b2ada7d068424d9b4c20219f862fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static02.png using Guid(c94b2ada7d068424d9b4c20219f862fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'be276e0550b2c322419ca31b9c2ff5e3') in 0.004543 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static04.png
  artifactKey: Guid(4dda1d1e2860448f0802e2032c1e9835) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static04.png using Guid(4dda1d1e2860448f0802e2032c1e9835) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '34b0d736ebb92cdc9554482b04a5d3d9') in 0.004634 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static07.png
  artifactKey: Guid(92d40b9c71eda4dfd83985e7fdc8572e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static07.png using Guid(92d40b9c71eda4dfd83985e7fdc8572e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b4fb48c3a0c2f4374ce9d3bca756d0d0') in 0.005469 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static03.png
  artifactKey: Guid(7d6b57cfe45da4c82afec5c97007d3b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static03.png using Guid(7d6b57cfe45da4c82afec5c97007d3b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c863a1ecef1ae174a9baedd986a837a4') in 0.005624 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001856 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.65 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.663 seconds
Domain Reload Profiling:
	ReloadAssembly (663ms)
		BeginReloadAssembly (81ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (32ms)
		EndReloadAssembly (525ms)
			LoadAssemblies (44ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (156ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (32ms)
			SetupLoadedEditorAssemblies (254ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (50ms)
				ProcessInitializeOnLoadAttributes (137ms)
				ProcessInitializeOnLoadMethodAttributes (53ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6182.
Memory consumption went from 217.8 MB to 216.5 MB.
Total: 3.806200 ms (FindLiveObjects: 0.389500 ms CreateObjectMapping: 0.225900 ms MarkObjects: 2.679100 ms  DeleteObjects: 0.510600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0