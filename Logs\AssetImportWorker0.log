Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.14f1 (eee1884e7226) revision 15655304'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 32577 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files\Unity\2021.3.14f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
E:/ProjectsUnity/City
-logFile
Logs/AssetImportWorker0.log
-srvPort
61627
Successfully changed project path to: E:/ProjectsUnity/City
E:/ProjectsUnity/City
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Refreshing native plugins compatible for Editor in 75.04 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.14f1 (eee1884e7226)
[Subsystems] Discovering subsystems at path D:/Program Files/Unity/2021.3.14f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/ProjectsUnity/City/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 Ti (ID=0x2803)
    Vendor:   NVIDIA
    VRAM:     7949 MB
    Driver:   32.0.15.6117
Initialize mono
Mono path[0] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56200
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001515 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 71.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.442 seconds
Domain Reload Profiling:
	ReloadAssembly (443ms)
		BeginReloadAssembly (38ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (344ms)
			LoadAssemblies (38ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (79ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (17ms)
			SetupLoadedEditorAssemblies (215ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (43ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (71ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (67ms)
				ProcessInitializeOnLoadMethodAttributes (33ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.002150 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 79.54 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.948 seconds
Domain Reload Profiling:
	ReloadAssembly (949ms)
		BeginReloadAssembly (200ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (146ms)
		EndReloadAssembly (684ms)
			LoadAssemblies (46ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (167ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (46ms)
			SetupLoadedEditorAssemblies (388ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (80ms)
				BeforeProcessingInitializeOnLoad (67ms)
				ProcessInitializeOnLoadAttributes (170ms)
				ProcessInitializeOnLoadMethodAttributes (60ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 0.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5726 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (1.2 MB). Loaded Objects now: 6167.
Memory consumption went from 221.1 MB to 219.9 MB.
Total: 2.542000 ms (FindLiveObjects: 0.255700 ms CreateObjectMapping: 0.103100 ms MarkObjects: 1.885500 ms  DeleteObjects: 0.297100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001814 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.68 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.675 seconds
Domain Reload Profiling:
	ReloadAssembly (676ms)
		BeginReloadAssembly (74ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (549ms)
			LoadAssemblies (44ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (154ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (33ms)
			SetupLoadedEditorAssemblies (271ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (52ms)
				ProcessInitializeOnLoadAttributes (147ms)
				ProcessInitializeOnLoadMethodAttributes (57ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.21 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6170.
Memory consumption went from 213.8 MB to 212.6 MB.
Total: 2.309500 ms (FindLiveObjects: 0.214700 ms CreateObjectMapping: 0.085200 ms MarkObjects: 1.706400 ms  DeleteObjects: 0.302500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001868 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.64 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.677 seconds
Domain Reload Profiling:
	ReloadAssembly (677ms)
		BeginReloadAssembly (77ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (553ms)
			LoadAssemblies (44ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (157ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (32ms)
			SetupLoadedEditorAssemblies (276ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (55ms)
				ProcessInitializeOnLoadAttributes (148ms)
				ProcessInitializeOnLoadMethodAttributes (57ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.33 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6173.
Memory consumption went from 213.8 MB to 212.6 MB.
Total: 2.333500 ms (FindLiveObjects: 0.212000 ms CreateObjectMapping: 0.077700 ms MarkObjects: 1.736500 ms  DeleteObjects: 0.306800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 3161.531452 seconds.
  path: Assets/Settings/Renderer2D.asset
  artifactKey: Guid(424799608f7334c24bf367e4bbfa7f9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/Renderer2D.asset using Guid(424799608f7334c24bf367e4bbfa7f9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e817a025e7e0e616456cca6a22a6cdc5') in 0.029297 seconds 
========================================================================
Received Import Request.
  Time since last request: 31.099048 seconds.
  path: Assets/Settings/Renderer2D.asset
  artifactKey: Guid(424799608f7334c24bf367e4bbfa7f9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/Renderer2D.asset using Guid(424799608f7334c24bf367e4bbfa7f9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '03c6c89c81bd1c6c38046c9c2696aca5') in 0.001315 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.012679 seconds.
  path: Assets/Settings/UniversalRP.asset
  artifactKey: Guid(681886c5eb7344803b6206f758bf0b1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/UniversalRP.asset using Guid(681886c5eb7344803b6206f758bf0b1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4937e178c8f8753c30cbc242dfb5b55d') in 0.001665 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001835 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.64 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.652 seconds
Domain Reload Profiling:
	ReloadAssembly (653ms)
		BeginReloadAssembly (80ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (525ms)
			LoadAssemblies (48ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (144ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (270ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (53ms)
				ProcessInitializeOnLoadAttributes (147ms)
				ProcessInitializeOnLoadMethodAttributes (55ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5610 Unused Serialized files (Serialized files now loaded: 0)
Unloading 47 unused Assets / (1.2 MB). Loaded Objects now: 6177.
Memory consumption went from 213.9 MB to 212.7 MB.
Total: 2.544000 ms (FindLiveObjects: 0.217900 ms CreateObjectMapping: 0.086800 ms MarkObjects: 1.686100 ms  DeleteObjects: 0.552500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 212.864846 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_46.png
  artifactKey: Guid(494805abc9a0585449ccf7818e78a834) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_46.png using Guid(494805abc9a0585449ccf7818e78a834) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '051e25825e0f17b3fd0bdb185cf65f7f') in 0.060456 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_63.png
  artifactKey: Guid(98f2642b52e0df044b6a3fbddbec1a11) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_63.png using Guid(98f2642b52e0df044b6a3fbddbec1a11) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8f881701b96f7459a0702819cdd33447') in 0.006254 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_67.png
  artifactKey: Guid(168d52d0af897854f926318ce9f0cf63) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_67.png using Guid(168d52d0af897854f926318ce9f0cf63) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '42d286057b1652a2072d6016c7591f12') in 0.006020 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_57.png
  artifactKey: Guid(aa22187d5a686b0449798b05aef5d043) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_57.png using Guid(aa22187d5a686b0449798b05aef5d043) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '25b6daef3b5435899d9c9747604c2de3') in 0.005860 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_54.png
  artifactKey: Guid(d0af17dc956c6d84b9b80a1345f2e01e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_54.png using Guid(d0af17dc956c6d84b9b80a1345f2e01e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bdd9895dccdd679302f3f475dc4651fa') in 0.005756 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_58.png
  artifactKey: Guid(778c64e443f8c8f4d9ef636747cb6ee8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_58.png using Guid(778c64e443f8c8f4d9ef636747cb6ee8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fbb5b92d8359cff4e74d15e330a37bfc') in 0.005290 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0