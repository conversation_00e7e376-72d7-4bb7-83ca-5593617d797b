Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.14f1 (eee1884e7226) revision 15655304'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 32577 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files\Unity\2021.3.14f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker4
-projectPath
E:/ProjectsUnity/City
-logFile
Logs/AssetImportWorker4.log
-srvPort
61627
Successfully changed project path to: E:/ProjectsUnity/City
E:/ProjectsUnity/City
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Refreshing native plugins compatible for Editor in 66.53 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.14f1 (eee1884e7226)
[Subsystems] Discovering subsystems at path D:/Program Files/Unity/2021.3.14f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/ProjectsUnity/City/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 Ti (ID=0x2803)
    Vendor:   NVIDIA
    VRAM:     7949 MB
    Driver:   32.0.15.6117
Initialize mono
Mono path[0] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56292
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001204 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 67.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.485 seconds
Domain Reload Profiling:
	ReloadAssembly (486ms)
		BeginReloadAssembly (62ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (350ms)
			LoadAssemblies (62ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (89ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (19ms)
			SetupLoadedEditorAssemblies (207ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (40ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (67ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (67ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.003207 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 77.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.071 seconds
Domain Reload Profiling:
	ReloadAssembly (1072ms)
		BeginReloadAssembly (167ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (113ms)
		EndReloadAssembly (835ms)
			LoadAssemblies (53ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (260ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (55ms)
			SetupLoadedEditorAssemblies (427ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (9ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (77ms)
				BeforeProcessingInitializeOnLoad (75ms)
				ProcessInitializeOnLoadAttributes (194ms)
				ProcessInitializeOnLoadMethodAttributes (69ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (4ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 0.87 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5726 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (1.2 MB). Loaded Objects now: 6167.
Memory consumption went from 221.1 MB to 219.9 MB.
Total: 3.671600 ms (FindLiveObjects: 0.354000 ms CreateObjectMapping: 0.168300 ms MarkObjects: 2.656500 ms  DeleteObjects: 0.491700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0