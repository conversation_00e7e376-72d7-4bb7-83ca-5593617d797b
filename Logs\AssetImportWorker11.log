Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.14f1 (eee1884e7226) revision 15655304'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 32577 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files\Unity\2021.3.14f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker11
-projectPath
E:/ProjectsUnity/City
-logFile
Logs/AssetImportWorker11.log
-srvPort
61627
Successfully changed project path to: E:/ProjectsUnity/City
E:/ProjectsUnity/City
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Refreshing native plugins compatible for Editor in 68.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.14f1 (eee1884e7226)
[Subsystems] Discovering subsystems at path D:/Program Files/Unity/2021.3.14f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/ProjectsUnity/City/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 Ti (ID=0x2803)
    Vendor:   NVIDIA
    VRAM:     7949 MB
    Driver:   32.0.15.6117
Initialize mono
Mono path[0] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56788
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002240 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 77.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.436 seconds
Domain Reload Profiling:
	ReloadAssembly (436ms)
		BeginReloadAssembly (44ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (326ms)
			LoadAssemblies (44ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (69ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (20ms)
			SetupLoadedEditorAssemblies (205ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (37ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (77ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (59ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.002609 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 79.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.972 seconds
Domain Reload Profiling:
	ReloadAssembly (973ms)
		BeginReloadAssembly (181ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (127ms)
		EndReloadAssembly (726ms)
			LoadAssemblies (50ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (184ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (48ms)
			SetupLoadedEditorAssemblies (403ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (8ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (79ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (169ms)
				ProcessInitializeOnLoadMethodAttributes (73ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Refreshing native plugins compatible for Editor in 0.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5727 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (1.2 MB). Loaded Objects now: 6169.
Memory consumption went from 221.4 MB to 220.2 MB.
Total: 3.486200 ms (FindLiveObjects: 0.321000 ms CreateObjectMapping: 0.165000 ms MarkObjects: 2.540100 ms  DeleteObjects: 0.459200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 26656.005694 seconds.
  path: Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/spider-64.png
  artifactKey: Guid(d1b72323dad76bc4da0da6c26f3dbc2f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/spider-64.png using Guid(d1b72323dad76bc4da0da6c26f3dbc2f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '173eea5f2dff4a43af4c57da40b1c33f') in 0.188769 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/StompyRobot/SRDebugger/UI/Sprites/Default/BG/footer_lodyas.png
  artifactKey: Guid(72f587d74eb73bb4789dd3f6f10b3600) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/UI/Sprites/Default/BG/footer_lodyas.png using Guid(72f587d74eb73bb4789dd3f6f10b3600) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '46a5a889ae67a8068bbfbbcce738846f') in 0.012643 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Pipeline Tile/TileAssets/Pipes.asset
  artifactKey: Guid(cac0cbe288401c94aa929d9b746b0f74) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Tiles/Pipeline Tile/TileAssets/Pipes.asset using Guid(cac0cbe288401c94aa929d9b746b0f74) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '245767391b7fe76a1b6e1fc18e0278bf') in 0.005205 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Trigger.prefab
  artifactKey: Guid(3024365d197ebaa4abedb991801f0b79) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Trigger.prefab using Guid(3024365d197ebaa4abedb991801f0b79) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'ad000b1a985de607ad627e43b1ec8308') in 0.028976 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/StompyRobot/SRDebugger/Scripts/UI/Controls/Profiler/ProfilerMemoryBlock.cs
  artifactKey: Guid(445cb6d0a347a0542a38dd652e9ba01b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Scripts/UI/Controls/Profiler/ProfilerMemoryBlock.cs using Guid(445cb6d0a347a0542a38dd652e9ba01b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '8e535938ab31f86835c00cfc67179e1a') in 0.009300 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Foliage Tile/Tile Asset/Foliage.asset
  artifactKey: Guid(34989aa4dce6c704d93f222c69e9218f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Foliage Tile/Tile Asset/Foliage.asset using Guid(34989aa4dce6c704d93f222c69e9218f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '080146f4d984bba7a4a7fddfe774ae57') in 0.007254 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Rule Override Tile/Tile Asset/Terrain.asset
  artifactKey: Guid(4eecde95157922c458b7abbb88b4db31) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Rule Override Tile/Tile Asset/Terrain.asset using Guid(4eecde95157922c458b7abbb88b4db31) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '322b3e3fecf7f36d1e3f951bacb3283a') in 0.006009 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Tabs/BugReporter.prefab
  artifactKey: Guid(c3059d02b0eb1694bb1ed7bf533a17c3) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Tabs/BugReporter.prefab using Guid(c3059d02b0eb1694bb1ed7bf533a17c3) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '92cbc10e4e6dd78b31f03774c20d1a7d') in 0.021379 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Tabs/Console.prefab
  artifactKey: Guid(abd74791d32aafa4c9ca4f20145cd14c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Tabs/Console.prefab using Guid(abd74791d32aafa4c9ca4f20145cd14c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '6db3f556af6d525471db5c229b481277') in 0.034003 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run E/witch run E05.png
  artifactKey: Guid(ea836e1730ade47a9a1cdc6ff307c4ee) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run E/witch run E05.png using Guid(ea836e1730ade47a9a1cdc6ff307c4ee) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '4caaee4db30e93c55e8e4e42e0dd2626') in 0.011423 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run E/witch run E04.png
  artifactKey: Guid(a8d723aaba3d14c6b908b65490c3ceb5) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run E/witch run E04.png using Guid(a8d723aaba3d14c6b908b65490c3ceb5) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '4dff961e8c2d9f58748097a758deaa96') in 0.013580 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Tabs/System.prefab
  artifactKey: Guid(b375b5741c9234f4c8e08b2f4c81e15d) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Tabs/System.prefab using Guid(b375b5741c9234f4c8e08b2f4c81e15d) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '4b9e049f1178d6ecf400b30428035097') in 0.011841 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Static W.anim
  artifactKey: Guid(9f42d9553e65f41d59f7a26e598f01d7) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Static W.anim using Guid(9f42d9553e65f41d59f7a26e598f01d7) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '9d38f0085a12ea66584b67588874068c') in 0.006121 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run NE/witch run NE00.png
  artifactKey: Guid(fd663cfefc8e74e17b188571fa2c2e93) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run NE/witch run NE00.png using Guid(fd663cfefc8e74e17b188571fa2c2e93) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'ced0092eba5b4d971081926304401949') in 0.010447 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Custom Rule Tile/Tile Asset/CustomGrassTile.asset
  artifactKey: Guid(99c7212dd085d0c41b2b695bbadcfa1a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Custom Rule Tile/Tile Asset/CustomGrassTile.asset using Guid(99c7212dd085d0c41b2b695bbadcfa1a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'ce04372ea6122ff0e02145f20b51834a') in 0.004165 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Platform Tile/Sprites/platform.png
  artifactKey: Guid(1a29632a08213734895a58fd312fa737) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Platform Tile/Sprites/platform.png using Guid(1a29632a08213734895a58fd312fa737) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '55313bfdc78f12b6407b2ba20f72e2e8') in 0.022471 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run NE/witch run NE03.png
  artifactKey: Guid(afa9a0b28bb294c648f0559d3bc0b82f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run NE/witch run NE03.png using Guid(afa9a0b28bb294c648f0559d3bc0b82f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '48aa1c577366c539fc82445710906e2f') in 0.012814 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Brushes/Random Brush/Tile Asset/TempleCarved_2.asset
  artifactKey: Guid(d6ccd4012cd9e48419a36676ca2e5539) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Brushes/Random Brush/Tile Asset/TempleCarved_2.asset using Guid(d6ccd4012cd9e48419a36676ca2e5539) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '03b388727be3937344eb25dc76757e17') in 0.005777 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/PinnedUI.prefab
  artifactKey: Guid(bb4f9efb6ebc3b84cae1ddbccbd53c6f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/PinnedUI.prefab using Guid(bb4f9efb6ebc3b84cae1ddbccbd53c6f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'e1b714ac970cfac610079ea1f92b1d9b') in 0.026227 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run N/witch run N05.png
  artifactKey: Guid(1a9bb936617da429795e9937212a2adb) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run N/witch run N05.png using Guid(1a9bb936617da429795e9937212a2adb) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'b75634839979676b7d33e28004e6dbdf') in 0.013407 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run S/witch run S00.png
  artifactKey: Guid(f8261f9aeeae7454a9c4f86328a964a4) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run S/witch run S00.png using Guid(f8261f9aeeae7454a9c4f86328a964a4) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'a99c9e5fc31a022b15b132e776013dcf') in 0.011498 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Run NE.anim
  artifactKey: Guid(b4bbedf099f194ddc8e210ecec38e4c1) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Run NE.anim using Guid(b4bbedf099f194ddc8e210ecec38e4c1) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '12013c001bfeb2be32a9a29ad6da5a8c') in 0.005486 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/Brushes/Random Brush/Tile Asset/TemplePlants_5.asset
  artifactKey: Guid(68eea7afc1d4d57408baad8249b76e40) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Brushes/Random Brush/Tile Asset/TemplePlants_5.asset using Guid(68eea7afc1d4d57408baad8249b76e40) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '055a40cb5551f8eccebb04656df0f7ce') in 0.004031 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_51.asset
  artifactKey: Guid(965ba50f795392b48864524b8956a79f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_51.asset using Guid(965ba50f795392b48864524b8956a79f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '9cb337273060d6f23e0061f7798bcda8') in 0.004818 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Platform Tile 3/Atlas/Platform3.spriteatlas
  artifactKey: Guid(190ba2672b6291840bce7f46513c1666) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Platform Tile 3/Atlas/Platform3.spriteatlas using Guid(190ba2672b6291840bce7f46513c1666) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '34c47897f536575b22dcd009bb49c4d9') in 0.005424 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Platform Tile 2/Tile Asset/platform2.asset
  artifactKey: Guid(823af85de11980d41aaeb8fd9a040f81) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Platform Tile 2/Tile Asset/platform2.asset using Guid(823af85de11980d41aaeb8fd9a040f81) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '20a43d4c1d5a1aea14d9c457654b66ad') in 0.003527 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/StompyRobot/SRDebugger/Scripts/UI/Other/SRTab.cs
  artifactKey: Guid(94b1f24ed4379dd4fab14add56e520a5) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Scripts/UI/Other/SRTab.cs using Guid(94b1f24ed4379dd4fab14add56e520a5) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '637a593ce42aa178fc882611f0d1c636') in 0.002782 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Custom Rule Tile/Tile Asset/Dungeon Blue.asset
  artifactKey: Guid(7bfd416f6fae774498c2b98e0f91cbe4) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Custom Rule Tile/Tile Asset/Dungeon Blue.asset using Guid(7bfd416f6fae774498c2b98e0f91cbe4) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '37c7515382cf0b6575a237de2bf8e3ce') in 0.004565 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run E/witch run E00.png
  artifactKey: Guid(6a761b108787f4628931198fbe0822d4) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run E/witch run E00.png using Guid(6a761b108787f4628931198fbe0822d4) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '65eac9eace90f161cfce5b7df212212a') in 0.013651 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/StompyRobot/SRF/Scripts/UI/Editor/CopyPreferredSizesEditor.cs
  artifactKey: Guid(393fb435a5c8c8043a1cbc34b977e315) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRF/Scripts/UI/Editor/CopyPreferredSizesEditor.cs using Guid(393fb435a5c8c8043a1cbc34b977e315) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'a6a7775c599a01a4afcff356fe25e83e') in 0.002999 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_33.asset
  artifactKey: Guid(9cddacc810f8f234e84580b194162513) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_33.asset using Guid(9cddacc810f8f234e84580b194162513) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '61ef2e93dd006c5ee096b40a785ccc83') in 0.005143 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/StompyRobot/SRDebugger/UI/Sprites/Default/RoundedCorner-2px.psd
  artifactKey: Guid(485f3bf8e01fab741915ba1f20bc5a8f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/UI/Sprites/Default/RoundedCorner-2px.psd using Guid(485f3bf8e01fab741915ba1f20bc5a8f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '7f43cc3b3249a4740cd9504bba1c0ae2') in 0.015915 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/StompyRobot/SRDebugger/Editor/Icons/Dark/console-25.png
  artifactKey: Guid(ec0451a83de031241b08885adca07a2a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Editor/Icons/Dark/console-25.png using Guid(ec0451a83de031241b08885adca07a2a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '2ae516cbc87ea2c43f287d76388483ec') in 0.011385 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Animated Ocean Tile/Atlas/Ocean.spriteatlas
  artifactKey: Guid(d282208e03c182143bd68d58caa469fc) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Animated Ocean Tile/Atlas/Ocean.spriteatlas using Guid(d282208e03c182143bd68d58caa469fc) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '1e10f0a481ed7cb861853798c370dc14') in 0.003930 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Prefabs/MainCamera_IsometricPixelPerfect.prefab
  artifactKey: Guid(a715e6498a8f8444e8af75aa518ea006) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Prefabs/MainCamera_IsometricPixelPerfect.prefab using Guid(a715e6498a8f8444e8af75aa518ea006) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '7da01caa3ce0676fcb566f794aeb1482') in 0.009974 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_15.asset
  artifactKey: Guid(67ceccca055867c4d808a65f1a0b67ac) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_15.asset using Guid(67ceccca055867c4d808a65f1a0b67ac) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '0d380c29bec9e32fcfd714b05c2769a8') in 0.004630 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/Destructible/Tile Assets/Undestructible.asset
  artifactKey: Guid(8e3635754d59b824ca8ab3d0ab07686f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Destructible/Tile Assets/Undestructible.asset using Guid(8e3635754d59b824ca8ab3d0ab07686f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '913a54edfaca78f44d4993a1aaea3f75') in 0.004274 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Brushes/Random Brush/Tile Asset/TemplePlants_9.asset
  artifactKey: Guid(39a72bc8509c04043842f2f0d00c8804) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Brushes/Random Brush/Tile Asset/TemplePlants_9.asset using Guid(39a72bc8509c04043842f2f0d00c8804) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '9634dd1209c9aeff526ef3cc7b028dbd') in 0.004052 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/StompyRobot/SRDebugger/UI/Prefabs/Console/Item.prefab
  artifactKey: Guid(408ed745743f47f46ac11b71f96cf1a4) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/UI/Prefabs/Console/Item.prefab using Guid(408ed745743f47f46ac11b71f96cf1a4) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '56e0c3a9a10d5dd41aa64960c95204ff') in 0.008575 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Platform Tile 3/Tile Asset/platform3.asset
  artifactKey: Guid(741b9ba1e1c83354db503a5ce9efa70e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Platform Tile 3/Tile Asset/platform3.asset using Guid(741b9ba1e1c83354db503a5ce9efa70e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '570b10d6791556c742b5747b58473e21') in 0.003555 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.560507 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run SE/witch run SE01.png
  artifactKey: Guid(466bf6a56c6434d4f82afc772b264ece) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run SE/witch run SE01.png using Guid(466bf6a56c6434d4f82afc772b264ece) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'eacd3b926a216b0cf6b076fd45c12c99') in 0.014107 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static04.png
  artifactKey: Guid(4dda1d1e2860448f0802e2032c1e9835) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static04.png using Guid(4dda1d1e2860448f0802e2032c1e9835) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '686169520fb80581402488a4f8baea54') in 0.013624 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_16.png
  artifactKey: Guid(fb021a0855ad898469c732016f6e3b9e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_16.png using Guid(fb021a0855ad898469c732016f6e3b9e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '3424c699973e8a672b5aa8ac05af99c4') in 0.013065 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_14.png
  artifactKey: Guid(2ee43175fe272a24ba3ef1aa441cae17) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_14.png using Guid(2ee43175fe272a24ba3ef1aa441cae17) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '251d35f7594c19715ce8e4c93bacf42a') in 0.013709 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Colliders/ColliderSprites/stairs-border.png
  artifactKey: Guid(063edbe48872b4ab39d61247bfbc7217) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Colliders/ColliderSprites/stairs-border.png using Guid(063edbe48872b4ab39d61247bfbc7217) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'b241e043ecbd68aa67e588c450525519') in 0.014507 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Colliders/ColliderTiles/tile-border-right.asset
  artifactKey: Guid(2655b78189c934c049014795a63848c2) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Colliders/ColliderTiles/tile-border-right.asset using Guid(2655b78189c934c049014795a63848c2) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '64c641bde22a52d3622832a1256080ee') in 0.004596 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_61.png
  artifactKey: Guid(3e6f4dd2f9f1d39498413685594f5bac) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_61.png using Guid(3e6f4dd2f9f1d39498413685594f5bac) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '59936f38c06a2e201dfc944a5cf62e10') in 0.010946 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Colliders/ColliderSprites/tile-border-right.png
  artifactKey: Guid(8b08bcd21cb8a42fba868f390b68099b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Colliders/ColliderSprites/tile-border-right.png using Guid(8b08bcd21cb8a42fba868f390b68099b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'be59d5d37da5a795512c32de3b8856e0') in 0.011175 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Colliders/ColliderTiles/stairs-border.asset
  artifactKey: Guid(cbf694c6d4cf7407fa98171d5b460804) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Colliders/ColliderTiles/stairs-border.asset using Guid(cbf694c6d4cf7407fa98171d5b460804) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '31bd8a87165c23ecb5535a5ad3535070') in 0.004653 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_01.png
  artifactKey: Guid(3997bbb2174dc6342bee8b8803a2ffe2) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_01.png using Guid(3997bbb2174dc6342bee8b8803a2ffe2) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '1519eccf4a99e3b0afc535c5c3a18811') in 0.011919 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_56.png
  artifactKey: Guid(1ca26f020a1a57145bd237282c36196f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_56.png using Guid(1ca26f020a1a57145bd237282c36196f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '9cdc26527feb241860d34a153e788b0d') in 0.011088 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_47.png
  artifactKey: Guid(daaaae34d0708154baf5d0353d396a41) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_47.png using Guid(daaaae34d0708154baf5d0353d396a41) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '7186935b7410c85193b3a5e6ab4c1c2b') in 0.010361 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_46.png
  artifactKey: Guid(494805abc9a0585449ccf7818e78a834) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_46.png using Guid(494805abc9a0585449ccf7818e78a834) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'e88bf91caacdfd3c468f8bcf9ce08adb') in 0.011267 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0