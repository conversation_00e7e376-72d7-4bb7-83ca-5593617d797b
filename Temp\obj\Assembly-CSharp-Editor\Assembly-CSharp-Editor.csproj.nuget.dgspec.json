{"format": 1, "restore": {"e:\\ProjectsUnity\\City\\Assembly-CSharp-Editor.csproj": {}}, "projects": {"e:\\ProjectsUnity\\City\\Assembly-CSharp-Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "e:\\ProjectsUnity\\City\\Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "e:\\ProjectsUnity\\City\\Assembly-CSharp-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "e:\\ProjectsUnity\\City\\Temp\\obj\\Assembly-CSharp-Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"e:\\ProjectsUnity\\City\\Assembly-CSharp.csproj": {"projectPath": "e:\\ProjectsUnity\\City\\Assembly-CSharp.csproj"}, "e:\\ProjectsUnity\\City\\StompyRobot.SRDebugger.csproj": {"projectPath": "e:\\ProjectsUnity\\City\\StompyRobot.SRDebugger.csproj"}, "e:\\ProjectsUnity\\City\\StompyRobot.SRDebugger.Editor.csproj": {"projectPath": "e:\\ProjectsUnity\\City\\StompyRobot.SRDebugger.Editor.csproj"}, "e:\\ProjectsUnity\\City\\StompyRobot.SRF.csproj": {"projectPath": "e:\\ProjectsUnity\\City\\StompyRobot.SRF.csproj"}, "e:\\ProjectsUnity\\City\\StompyRobot.SRF.Editor.csproj": {"projectPath": "e:\\ProjectsUnity\\City\\StompyRobot.SRF.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.100\\RuntimeIdentifierGraph.json"}}}, "e:\\ProjectsUnity\\City\\Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "e:\\ProjectsUnity\\City\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "e:\\ProjectsUnity\\City\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "e:\\ProjectsUnity\\City\\Temp\\obj\\Assembly-CSharp\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"e:\\ProjectsUnity\\City\\StompyRobot.SRDebugger.csproj": {"projectPath": "e:\\ProjectsUnity\\City\\StompyRobot.SRDebugger.csproj"}, "e:\\ProjectsUnity\\City\\StompyRobot.SRDebugger.Editor.csproj": {"projectPath": "e:\\ProjectsUnity\\City\\StompyRobot.SRDebugger.Editor.csproj"}, "e:\\ProjectsUnity\\City\\StompyRobot.SRF.csproj": {"projectPath": "e:\\ProjectsUnity\\City\\StompyRobot.SRF.csproj"}, "e:\\ProjectsUnity\\City\\StompyRobot.SRF.Editor.csproj": {"projectPath": "e:\\ProjectsUnity\\City\\StompyRobot.SRF.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.100\\RuntimeIdentifierGraph.json"}}}, "e:\\ProjectsUnity\\City\\StompyRobot.SRDebugger.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "e:\\ProjectsUnity\\City\\StompyRobot.SRDebugger.csproj", "projectName": "StompyRobot.SRDebugger", "projectPath": "e:\\ProjectsUnity\\City\\StompyRobot.SRDebugger.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "e:\\ProjectsUnity\\City\\Temp\\obj\\StompyRobot.SRDebugger\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"e:\\ProjectsUnity\\City\\StompyRobot.SRF.csproj": {"projectPath": "e:\\ProjectsUnity\\City\\StompyRobot.SRF.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.100\\RuntimeIdentifierGraph.json"}}}, "e:\\ProjectsUnity\\City\\StompyRobot.SRDebugger.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "e:\\ProjectsUnity\\City\\StompyRobot.SRDebugger.Editor.csproj", "projectName": "StompyRobot.SRDebugger.Editor", "projectPath": "e:\\ProjectsUnity\\City\\StompyRobot.SRDebugger.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "e:\\ProjectsUnity\\City\\Temp\\obj\\StompyRobot.SRDebugger.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"e:\\ProjectsUnity\\City\\StompyRobot.SRDebugger.csproj": {"projectPath": "e:\\ProjectsUnity\\City\\StompyRobot.SRDebugger.csproj"}, "e:\\ProjectsUnity\\City\\StompyRobot.SRF.csproj": {"projectPath": "e:\\ProjectsUnity\\City\\StompyRobot.SRF.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.100\\RuntimeIdentifierGraph.json"}}}, "e:\\ProjectsUnity\\City\\StompyRobot.SRF.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "e:\\ProjectsUnity\\City\\StompyRobot.SRF.csproj", "projectName": "StompyRobot.SRF", "projectPath": "e:\\ProjectsUnity\\City\\StompyRobot.SRF.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "e:\\ProjectsUnity\\City\\Temp\\obj\\StompyRobot.SRF\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.100\\RuntimeIdentifierGraph.json"}}}, "e:\\ProjectsUnity\\City\\StompyRobot.SRF.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "e:\\ProjectsUnity\\City\\StompyRobot.SRF.Editor.csproj", "projectName": "StompyRobot.SRF.Editor", "projectPath": "e:\\ProjectsUnity\\City\\StompyRobot.SRF.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "e:\\ProjectsUnity\\City\\Temp\\obj\\StompyRobot.SRF.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"e:\\ProjectsUnity\\City\\StompyRobot.SRF.csproj": {"projectPath": "e:\\ProjectsUnity\\City\\StompyRobot.SRF.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.100\\RuntimeIdentifierGraph.json"}}}}}