Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.14f1 (eee1884e7226) revision 15655304'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 32577 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files\Unity\2021.3.14f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
E:/ProjectsUnity/City
-logFile
Logs/AssetImportWorker1.log
-srvPort
52262
Successfully changed project path to: E:/ProjectsUnity/City
E:/ProjectsUnity/City
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Refreshing native plugins compatible for Editor in 66.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.14f1 (eee1884e7226)
[Subsystems] Discovering subsystems at path D:/Program Files/Unity/2021.3.14f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/ProjectsUnity/City/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 Ti (ID=0x2803)
    Vendor:   NVIDIA
    VRAM:     7949 MB
    Driver:   32.0.15.6117
Initialize mono
Mono path[0] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56308
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001474 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 66.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.393 seconds
Domain Reload Profiling:
	ReloadAssembly (393ms)
		BeginReloadAssembly (39ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (295ms)
			LoadAssemblies (39ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (60ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (18ms)
			SetupLoadedEditorAssemblies (188ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (31ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (67ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (60ms)
				ProcessInitializeOnLoadMethodAttributes (29ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.002879 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 65.65 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.882 seconds
Domain Reload Profiling:
	ReloadAssembly (883ms)
		BeginReloadAssembly (174ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (125ms)
		EndReloadAssembly (646ms)
			LoadAssemblies (50ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (171ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (46ms)
			SetupLoadedEditorAssemblies (350ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (66ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (150ms)
				ProcessInitializeOnLoadMethodAttributes (54ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (4ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 0.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5726 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (1.2 MB). Loaded Objects now: 6166.
Memory consumption went from 221.0 MB to 219.7 MB.
Total: 2.554700 ms (FindLiveObjects: 0.247700 ms CreateObjectMapping: 0.088800 ms MarkObjects: 1.906600 ms  DeleteObjects: 0.310800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002473 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.77 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.731 seconds
Domain Reload Profiling:
	ReloadAssembly (732ms)
		BeginReloadAssembly (77ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (25ms)
		EndReloadAssembly (607ms)
			LoadAssemblies (46ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (163ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (35ms)
			SetupLoadedEditorAssemblies (309ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (59ms)
				ProcessInitializeOnLoadAttributes (166ms)
				ProcessInitializeOnLoadMethodAttributes (65ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5608 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6169.
Memory consumption went from 213.5 MB to 212.3 MB.
Total: 3.333300 ms (FindLiveObjects: 0.387500 ms CreateObjectMapping: 0.088900 ms MarkObjects: 2.557900 ms  DeleteObjects: 0.298500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 19755.393814 seconds.
  path: Assets/_MyGame/Tilemap/PaletteSwap/Tile Palettes/Swap Palette B.prefab
  artifactKey: Guid(91a5d3b00f9afd64eb0824d137f225ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/PaletteSwap/Tile Palettes/Swap Palette B.prefab using Guid(91a5d3b00f9afd64eb0824d137f225ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e1640a22b188fe942dfcc699834f58df') in 0.155756 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002018 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.719 seconds
Domain Reload Profiling:
	ReloadAssembly (719ms)
		BeginReloadAssembly (79ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (28ms)
		EndReloadAssembly (593ms)
			LoadAssemblies (46ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (152ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (309ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (68ms)
				ProcessInitializeOnLoadAttributes (168ms)
				ProcessInitializeOnLoadMethodAttributes (57ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5615 Unused Serialized files (Serialized files now loaded: 0)
Unloading 46 unused Assets / (1.2 MB). Loaded Objects now: 6244.
Memory consumption went from 220.4 MB to 219.2 MB.
Total: 2.337600 ms (FindLiveObjects: 0.225200 ms CreateObjectMapping: 0.094100 ms MarkObjects: 1.726000 ms  DeleteObjects: 0.291800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001890 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.89 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.672 seconds
Domain Reload Profiling:
	ReloadAssembly (672ms)
		BeginReloadAssembly (74ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (551ms)
			LoadAssemblies (46ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (142ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (274ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (53ms)
				ProcessInitializeOnLoadAttributes (149ms)
				ProcessInitializeOnLoadMethodAttributes (55ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5616 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6247.
Memory consumption went from 220.8 MB to 219.5 MB.
Total: 2.554900 ms (FindLiveObjects: 0.231800 ms CreateObjectMapping: 0.088800 ms MarkObjects: 1.935300 ms  DeleteObjects: 0.298600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 451.824205 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_1.asset
  artifactKey: Guid(1e85264348b17504f8bcd3144725a051) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_1.asset using Guid(1e85264348b17504f8bcd3144725a051) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f6faa3abe550da2bab87fa6cb749f794') in 0.035843 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.001045 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_23.asset
  artifactKey: Guid(03b192242e740ff48856ed821b0c5dd4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_23.asset using Guid(03b192242e740ff48856ed821b0c5dd4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1181b7ef91dd8d3ba4ccf307997e6b59') in 0.004857 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_27.asset
  artifactKey: Guid(30f98488c97193a4297e833e71136f5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_27.asset using Guid(30f98488c97193a4297e833e71136f5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6d2a0eb8961db1e9078af4e8e0bd73f4') in 0.004375 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_67.asset
  artifactKey: Guid(525f5d28cf7649e4cb66078fd815f957) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_67.asset using Guid(525f5d28cf7649e4cb66078fd815f957) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a05b7ead69997476a0be68c813ef6db6') in 0.004161 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_58.asset
  artifactKey: Guid(590f406acc3b8fe4d8df9f30e28b2ff5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_58.asset using Guid(590f406acc3b8fe4d8df9f30e28b2ff5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '93a4781a811770229492f3367f5eb815') in 0.004586 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_54.asset
  artifactKey: Guid(923d50e69b47abb4ab5260b135068b76) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_54.asset using Guid(923d50e69b47abb4ab5260b135068b76) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '864aee3485237df1903769ade0e8a0b7') in 0.005107 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_51.asset
  artifactKey: Guid(965ba50f795392b48864524b8956a79f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_51.asset using Guid(965ba50f795392b48864524b8956a79f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8238a896eba77b58b28407b276bfab7c') in 0.004775 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_78.asset
  artifactKey: Guid(9a4e5c7d78ae3a94980efa6f55d79a28) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_78.asset using Guid(9a4e5c7d78ae3a94980efa6f55d79a28) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f5b6bb0620f36ddcbac005543d2665a0') in 0.004706 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_42.asset
  artifactKey: Guid(726d86b504c51254f9a346ec48104422) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_42.asset using Guid(726d86b504c51254f9a346ec48104422) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7674464ff134c66250d523f0ae447daa') in 0.004741 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_61.asset
  artifactKey: Guid(c083ca08c2f52f14395c55a9ce429b40) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_61.asset using Guid(c083ca08c2f52f14395c55a9ce429b40) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '52a51559b62d1217092480b0e5762b4c') in 0.003752 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_73.asset
  artifactKey: Guid(4ddc98b9b1ae2d14b8aecac59017e16f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_73.asset using Guid(4ddc98b9b1ae2d14b8aecac59017e16f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '080b913555a8015606ccfa878b37c564') in 0.003959 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_21.asset
  artifactKey: Guid(c2edb7f837af3a84c835e2c5d6060e89) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_21.asset using Guid(c2edb7f837af3a84c835e2c5d6060e89) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f10ed22a20f1e6471fcb635f6b4c527d') in 0.003489 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_35.asset
  artifactKey: Guid(c2710e0fc0a820749b0dffba2a32004d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_35.asset using Guid(c2710e0fc0a820749b0dffba2a32004d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e7cc9387242ae57c6fe4dbdea90596ae') in 0.003726 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_77.asset
  artifactKey: Guid(dad24a8e78809c74c8ebff5b2c61d039) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_77.asset using Guid(dad24a8e78809c74c8ebff5b2c61d039) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e9b1e8b08ab66df4a497cfef71d4eb1b') in 0.003985 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_68.asset
  artifactKey: Guid(392d7d8cda3be2941bb1180eaa57cba4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_68.asset using Guid(392d7d8cda3be2941bb1180eaa57cba4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e1fa34255fa9a96fa2a8530323e3abde') in 0.003987 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_2.asset
  artifactKey: Guid(25c9d4a577533a84e9d094aea30a3464) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_2.asset using Guid(25c9d4a577533a84e9d094aea30a3464) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ff5ef12e2b73a45bf36d8c104d4c7776') in 0.004537 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_28.asset
  artifactKey: Guid(8f4c75f44fb548d4a9a2d18b0fe71efd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_28.asset using Guid(8f4c75f44fb548d4a9a2d18b0fe71efd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '44e961688b988d19b8390ab085a7e082') in 0.004263 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_50.asset
  artifactKey: Guid(141042402d5ede340bcc58b815f7f9c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_50.asset using Guid(141042402d5ede340bcc58b815f7f9c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b86c62f95b100607d154a77c5eb6a730') in 0.004013 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_34.asset
  artifactKey: Guid(8ff0906348c557d41abc64ba836c4406) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_34.asset using Guid(8ff0906348c557d41abc64ba836c4406) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '296feefa484410663618b8319e38406a') in 0.003357 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_29.asset
  artifactKey: Guid(1aa983282f0503a4ba3282e82c8cd3f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_29.asset using Guid(1aa983282f0503a4ba3282e82c8cd3f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9148003156a5a0f8ae6eedd3b6c9e1b0') in 0.004257 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_43.asset
  artifactKey: Guid(b83225a1d4a7c6f40ad0a4fe67262851) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_43.asset using Guid(b83225a1d4a7c6f40ad0a4fe67262851) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2dedb0994f5b1dd35f34ce82bb034162') in 0.004121 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_63.asset
  artifactKey: Guid(9057d0c54e3d073479511e57865d4e8e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_63.asset using Guid(9057d0c54e3d073479511e57865d4e8e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5c841ed1c8ea897e80f977ad42c16b05') in 0.004459 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_52.asset
  artifactKey: Guid(54ea36bde4a235646a62e2949556e387) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_52.asset using Guid(54ea36bde4a235646a62e2949556e387) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '152c5f95def377369a0fd76df0704e05') in 0.004342 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_15.asset
  artifactKey: Guid(67ceccca055867c4d808a65f1a0b67ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_15.asset using Guid(67ceccca055867c4d808a65f1a0b67ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b38ef5e906426bcf0e819678b37eb8a5') in 0.004356 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_55.asset
  artifactKey: Guid(8c86a6eb50e47d44caf18ab407bf1034) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_55.asset using Guid(8c86a6eb50e47d44caf18ab407bf1034) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8e3179815915ff662814e182d58db1db') in 0.004752 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_31.asset
  artifactKey: Guid(e6e67a065013c6741a2a0c55249e66f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_31.asset using Guid(e6e67a065013c6741a2a0c55249e66f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e6dfe18c9a0ebd1a3cadf0d834f4ed25') in 0.005897 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_45.asset
  artifactKey: Guid(2e08795a8650e514ea04fb9bb11b2a3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_45.asset using Guid(2e08795a8650e514ea04fb9bb11b2a3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fa7e69e1b710f1037a474aec14b8d64b') in 0.003737 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_39.asset
  artifactKey: Guid(9d7ebaa5bbd4158428750568934cfcf6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_39.asset using Guid(9d7ebaa5bbd4158428750568934cfcf6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f0b380dfe8c80771a3eb795082d7bb66') in 0.003560 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_14.asset
  artifactKey: Guid(a8bbf6594c3865c40b994d54f76544c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_14.asset using Guid(a8bbf6594c3865c40b994d54f76544c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'be56a5d7c67fa3db1f58cda719787c11') in 0.003726 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.998549 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_80.asset
  artifactKey: Guid(94055fe246a7c7748ad365be06568bcc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_80.asset using Guid(94055fe246a7c7748ad365be06568bcc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '76e0dabb79a23c1398474d4f2dd8074c') in 0.004524 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_81.asset
  artifactKey: Guid(d35e1af347ecf7d40bad995b47e3907e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Tiles/Palette/phlosioneer_hex_coastline_81.asset using Guid(d35e1af347ecf7d40bad995b47e3907e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '566fd91d1ff643923ba09461ed31fa95') in 0.004962 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0