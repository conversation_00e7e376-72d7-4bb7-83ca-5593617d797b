Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.14f1 (eee1884e7226) revision 15655304'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 32577 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files\Unity\2021.3.14f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker14
-projectPath
E:/ProjectsUnity/City
-logFile
Logs/AssetImportWorker14.log
-srvPort
61627
Successfully changed project path to: E:/ProjectsUnity/City
E:/ProjectsUnity/City
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Refreshing native plugins compatible for Editor in 76.65 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.14f1 (eee1884e7226)
[Subsystems] Discovering subsystems at path D:/Program Files/Unity/2021.3.14f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/ProjectsUnity/City/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 Ti (ID=0x2803)
    Vendor:   NVIDIA
    VRAM:     7949 MB
    Driver:   32.0.15.6117
Initialize mono
Mono path[0] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56864
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001266 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 75.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.440 seconds
Domain Reload Profiling:
	ReloadAssembly (440ms)
		BeginReloadAssembly (42ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (331ms)
			LoadAssemblies (42ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (65ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (20ms)
			SetupLoadedEditorAssemblies (211ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (37ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (76ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (65ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.002324 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 75.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.956 seconds
Domain Reload Profiling:
	ReloadAssembly (956ms)
		BeginReloadAssembly (170ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (117ms)
		EndReloadAssembly (723ms)
			LoadAssemblies (50ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (188ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (51ms)
			SetupLoadedEditorAssemblies (401ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (76ms)
				BeforeProcessingInitializeOnLoad (75ms)
				ProcessInitializeOnLoadAttributes (173ms)
				ProcessInitializeOnLoadMethodAttributes (65ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (4ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Refreshing native plugins compatible for Editor in 0.95 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5727 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (1.2 MB). Loaded Objects now: 6169.
Memory consumption went from 221.4 MB to 220.2 MB.
Total: 3.617400 ms (FindLiveObjects: 0.345400 ms CreateObjectMapping: 0.150300 ms MarkObjects: 2.662800 ms  DeleteObjects: 0.457800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 26656.001172 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Run N.anim
  artifactKey: Guid(fb80351f3d8f44225b396b3369cd6a6a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationClips/Run N.anim using Guid(fb80351f3d8f44225b396b3369cd6a6a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '9b393d50f601455a2a33806efa2d31c1') in 0.158650 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Random Tile/Tile Palettes/Random Tiles.prefab
  artifactKey: Guid(cb5a89d7e5f25ae469cb8159b274560e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Tiles/Random Tile/Tile Palettes/Random Tiles.prefab using Guid(cb5a89d7e5f25ae469cb8159b274560e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '84e2a9e61b4d9dd73922b5c1fc77a277') in 0.019128 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Random Tile/Tile Assets/RandomTempleStoneTile.asset
  artifactKey: Guid(4ee8c87ccf3b73844863c59efa6d5c05) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Tiles/Random Tile/Tile Assets/RandomTempleStoneTile.asset using Guid(4ee8c87ccf3b73844863c59efa6d5c05) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '1474d1c614647a92d004a07f80bb9d96') in 0.004526 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Platform Tile 2/Atlas/Platform2.spriteatlas
  artifactKey: Guid(908abedecc317094eb86fb4a1d44c5db) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Platform Tile 2/Atlas/Platform2.spriteatlas using Guid(908abedecc317094eb86fb4a1d44c5db) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'fd51487efe2d985b1bb4e3a296798826') in 0.008660 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Platform Tile/Atlas/Platformer.spriteatlas
  artifactKey: Guid(38100059a940f014dac730fb9eebee32) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Platform Tile/Atlas/Platformer.spriteatlas using Guid(38100059a940f014dac730fb9eebee32) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'ed7890dc979dc1a3727d5c8c41584efc') in 0.003522 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Weighted Random Tile/Tile Palettes/Weighted Random Tiles.prefab
  artifactKey: Guid(aa78802e56f101e4e9ffc00f917c1c1c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Tiles/Weighted Random Tile/Tile Palettes/Weighted Random Tiles.prefab using Guid(aa78802e56f101e4e9ffc00f917c1c1c) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'b6799dd4ea6b18b5d82ed57553f6be51') in 0.004718 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/StompyRobot/SRDebugger/Scripts/UI/Controls/Data/StringControl.cs
  artifactKey: Guid(019f81638c7cf1f438569f1ca9def175) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Scripts/UI/Controls/Data/StringControl.cs using Guid(019f81638c7cf1f438569f1ca9def175) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '37a60e2067e182e572bfedb25d9e95b4') in 0.007424 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/StompyRobot/SRDebugger/Scripts/UI/Controls/Data/EnumControl.cs
  artifactKey: Guid(3b7f14311f0b3f442b273192dc8ffe4d) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Scripts/UI/Controls/Data/EnumControl.cs using Guid(3b7f14311f0b3f442b273192dc8ffe4d) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '32e88e081315e95679bdc95329f00612') in 0.003317 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Pipeline Tile/Atlas/Pipeline.spriteatlas
  artifactKey: Guid(046019bab7759ba4c87adb65f3cb0ebd) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Pipeline Tile/Atlas/Pipeline.spriteatlas using Guid(046019bab7759ba4c87adb65f3cb0ebd) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'f8aeab39e416b6ea2450335657673f87') in 0.003472 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/StompyRobot/SRDebugger/Scripts/UI/Controls/Data/DataBoundControl.cs
  artifactKey: Guid(5f319c80d34ef2e4590641a136c82f1e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Scripts/UI/Controls/Data/DataBoundControl.cs using Guid(5f319c80d34ef2e4590641a136c82f1e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'eb73c255e9464ffe697b9c5ea286b8e0') in 0.003596 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/Filter-50.png
  artifactKey: Guid(2ccea1a2a1be59649956a168449bdb9f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/Filter-50.png using Guid(2ccea1a2a1be59649956a168449bdb9f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'f01ab928f77c149489cbbcc308b8cb52') in 0.029686 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Prefabs/Decorations/Desert/Tent.prefab
  artifactKey: Guid(6ed2348d3a21bb14199acf0512d8afb4) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Prefabs/Decorations/Desert/Tent.prefab using Guid(6ed2348d3a21bb14199acf0512d8afb4) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '3c7247bd0257aba1ef5db4723061b4fc') in 0.007432 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run S/witch run S03.png
  artifactKey: Guid(d25c0aca1001941689a8ea7705ec4bd3) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run S/witch run S03.png using Guid(d25c0aca1001941689a8ea7705ec4bd3) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'd05882b486247caa2fa80c365b060a6c') in 0.014417 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run N/witch run N01.png
  artifactKey: Guid(209f0f2ca47ab4d8bb6397b267227942) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run N/witch run N01.png using Guid(209f0f2ca47ab4d8bb6397b267227942) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'c18963e6b487ffe6c7d69afc119a66f5') in 0.013641 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Prefabs/Decorations/Desert/Rock_Purple_Large.prefab
  artifactKey: Guid(f03b714ecc5c721458f27961c794d663) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Prefabs/Decorations/Desert/Rock_Purple_Large.prefab using Guid(f03b714ecc5c721458f27961c794d663) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '951e44fa2b78fbc2687ae7e252c6f931') in 0.009075 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Options/StringOption.prefab
  artifactKey: Guid(bf3001d52880d2342a654b878fdae9d5) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Resources/SRDebugger/UI/Prefabs/Options/StringOption.prefab using Guid(bf3001d52880d2342a654b878fdae9d5) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '5b3428d3666e76a1c47d971f32834e1e') in 0.019859 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run NE/witch run NE02.png
  artifactKey: Guid(2c68d0b7014e14826a6b75082f00b353) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run NE/witch run NE02.png using Guid(2c68d0b7014e14826a6b75082f00b353) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'ae1bcfb9c578c1c9d8298150fdc8129b') in 0.013437 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run N/witch run N03.png
  artifactKey: Guid(3703359fc43e44bf688656e278de0013) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run N/witch run N03.png using Guid(3703359fc43e44bf688656e278de0013) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '1595190ee2c71b7d60ef9060f6cdd39b') in 0.011051 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Prefabs/Decorations/Desert/Rock_Purple_Small.prefab
  artifactKey: Guid(2743e1476b242f44092e871840e6225b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Prefabs/Decorations/Desert/Rock_Purple_Small.prefab using Guid(2743e1476b242f44092e871840e6225b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'c573f35075203ecd6d7ea9ea891bcd73') in 0.008143 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/StompyRobot/SRDebugger/Scripts/UI/Controls/Data/BoolControl.cs
  artifactKey: Guid(645dcae76b625be40b595e4bbc27abb6) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Scripts/UI/Controls/Data/BoolControl.cs using Guid(645dcae76b625be40b595e4bbc27abb6) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '48214086fef289cc1747d522fd42ad64') in 0.003164 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run S/witch run S04.png
  artifactKey: Guid(ea2a9989ee4bc4808987ac7796b6f02f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run S/witch run S04.png using Guid(ea2a9989ee4bc4808987ac7796b6f02f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '8fcaa63fef70cc3fa7bded7e8d4106ca') in 0.010978 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Rule Override Tile/Sprites/tilemap2.png
  artifactKey: Guid(2eff0de609d46d042bb46c56ac688c42) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Rule Override Tile/Sprites/tilemap2.png using Guid(2eff0de609d46d042bb46c56ac688c42) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '3f60177ac18bbf346b14d764467b44c5') in 0.094188 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Pin/<EMAIL>
  artifactKey: Guid(3967d34dc919aa04cac871bbf2ebce4b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Pin/<EMAIL> using Guid(3967d34dc919aa04cac871bbf2ebce4b) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'be25b8fdfea08683fd3674d087f29678') in 0.014853 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run E/witch run E03.png
  artifactKey: Guid(2e35d3710d38c424aa071ab80a02c383) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run E/witch run E03.png using Guid(2e35d3710d38c424aa071ab80a02c383) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'fa276590647bbe4411a8195956d77c9c') in 0.010439 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/menu-25.png
  artifactKey: Guid(a123c1c6f56057e4e9a11e2dcaf2df94) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/UI/Sprites/Default/Icons/menu-25.png using Guid(a123c1c6f56057e4e9a11e2dcaf2df94) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'cecb0b21f6d1187ac58dd03c9b003385') in 0.012841 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/Rule Tiles/Palette/Rule Override Tiles.prefab
  artifactKey: Guid(6112de5d7d0c01f4bb165e63c4716298) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Rule Tiles/Palette/Rule Override Tiles.prefab using Guid(6112de5d7d0c01f4bb165e63c4716298) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '26f736e698c69aa5413eacd388f00d0f') in 0.009959 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/StompyRobot/SRF/Scripts/UI/Editor/StyleComponentEditor.cs
  artifactKey: Guid(ffea2a0fd78a7484f88b5b877c0d6659) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRF/Scripts/UI/Editor/StyleComponentEditor.cs using Guid(ffea2a0fd78a7484f88b5b877c0d6659) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '5521ac6e3517cbbd0dde01e8f3965448') in 0.003437 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Terrain Tile/Tile Assets/PBricks.asset
  artifactKey: Guid(b28b928dff2a98f4d8b8c64582e76624) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Tiles/Terrain Tile/Tile Assets/PBricks.asset using Guid(b28b928dff2a98f4d8b8c64582e76624) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '9998ec2cf19268eb0d31a5ebb398c3b2') in 0.002369 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run N/witch run N02.png
  artifactKey: Guid(72c4c1fb466224ed1a081b31a30bda12) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run N/witch run N02.png using Guid(72c4c1fb466224ed1a081b31a30bda12) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '1e7b52d0482025b65f58c796281e1b3a') in 0.014164 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/StompyRobot/SRDebugger/Scripts/UI/Tabs/ConsoleTabController.cs
  artifactKey: Guid(7b54f4d46edbb634985db4a2fa4ada2f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/StompyRobot/SRDebugger/Scripts/UI/Tabs/ConsoleTabController.cs using Guid(7b54f4d46edbb634985db4a2fa4ada2f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'c1db52f00159485883b8515309a77722') in 0.003044 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/_MyGame/Tilemap/Hexagonal/Sprites/phlosioneer_hex_coastline.png
  artifactKey: Guid(26700e8cdbc04874eb40c414c72c5400) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/Hexagonal/Sprites/phlosioneer_hex_coastline.png using Guid(26700e8cdbc04874eb40c414c72c5400) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'a5a41b768cbb3df12b057aacc1ba56c7') in 0.222026 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.448513 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run SE/witch run SE05.png
  artifactKey: Guid(1d638e07f62b140fc8c3604b32bbaa3a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Run SE/witch run SE05.png using Guid(1d638e07f62b140fc8c3604b32bbaa3a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '34eb2afa304cda7c9b78d7bbd6c9f982') in 0.013839 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static03.png
  artifactKey: Guid(7d6b57cfe45da4c82afec5c97007d3b6) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Characters/Witch/AnimationSprites/Static/witch static03.png using Guid(7d6b57cfe45da4c82afec5c97007d3b6) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '5a019ef568411039162a2566438703c1') in 0.013925 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_28.png
  artifactKey: Guid(39f1c328a0d51dc4884a9e205c45d9aa) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_28.png using Guid(39f1c328a0d51dc4884a9e205c45d9aa) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '62aa7c8de1bd3a892fb3bd4d0c0fd701') in 0.011963 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_37.png
  artifactKey: Guid(9d9eaa5596fe0024187edb8f8035bd8a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_37.png using Guid(9d9eaa5596fe0024187edb8f8035bd8a) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'bfafba7f04848ae9063b97eb257812f1') in 0.012806 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_67.png
  artifactKey: Guid(168d52d0af897854f926318ce9f0cf63) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_67.png using Guid(168d52d0af897854f926318ce9f0cf63) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '35c7106d5ef7ca1e31192b118dd5e5cc') in 0.010493 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_05.png
  artifactKey: Guid(ff182ddc523cdc949978ddbb764a0fa3) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_05.png using Guid(ff182ddc523cdc949978ddbb764a0fa3) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '2a6e16ef58b7646eb62927dd763c419c') in 0.010857 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_41.png
  artifactKey: Guid(bf7ebbf343cee7c4b9c1a7c3f629ecfe) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_41.png using Guid(bf7ebbf343cee7c4b9c1a7c3f629ecfe) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'cd6122ffae92c276dd3254e3ca926ed8') in 0.011307 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_17.png
  artifactKey: Guid(4495010f0f608e84498564b6ae27cfff) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Flat/desert-sliced_17.png using Guid(4495010f0f608e84498564b6ae27cfff) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'b92f7d8aa87346cecff52db136279215') in 0.010748 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_58.png
  artifactKey: Guid(778c64e443f8c8f4d9ef636747cb6ee8) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_58.png using Guid(778c64e443f8c8f4d9ef636747cb6ee8) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '9d915e90491ff993aafeb361404f9a49') in 0.010499 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_38.png
  artifactKey: Guid(3131263726b2e8447adae796ef02ddb5) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Raised/desert-sliced_38.png using Guid(3131263726b2e8447adae796ef02ddb5) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '77c595da41338ba129ca5ca56407de98') in 0.012045 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_54.png
  artifactKey: Guid(d0af17dc956c6d84b9b80a1345f2e01e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_54.png using Guid(d0af17dc956c6d84b9b80a1345f2e01e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: 'f5fbb09f736029ba0e0d3e19b1644417') in 0.010672 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_65.png
  artifactKey: Guid(63a584ccc0f46ff4389d2e18fc09a742) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_65.png using Guid(63a584ccc0f46ff4389d2e18fc09a742) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '07d36758c2ec6f6ee28ec77ca668935e') in 0.014481 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_57.png
  artifactKey: Guid(aa22187d5a686b0449798b05aef5d043) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/_MyGame/Tilemap/IsometricZAsY/Tilemaps/Isometric/Sprites/Desert/Decoration/desert-sliced_57.png using Guid(aa22187d5a686b0449798b05aef5d043) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) Generated GUIDs only implemented for interfaces!
 -> (artifact id: '4e2b30a910a4b56c5998b5cf00cd7997') in 0.011772 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002023 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.656 seconds
Domain Reload Profiling:
	ReloadAssembly (657ms)
		BeginReloadAssembly (88ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (37ms)
		EndReloadAssembly (519ms)
			LoadAssemblies (46ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (158ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (251ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (50ms)
				ProcessInitializeOnLoadAttributes (136ms)
				ProcessInitializeOnLoadMethodAttributes (50ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5609 Unused Serialized files (Serialized files now loaded: 0)
Unloading 47 unused Assets / (90.4 MB). Loaded Objects now: 6192.
Memory consumption went from 214.2 MB to 123.8 MB.
Total: 4.118000 ms (FindLiveObjects: 0.223100 ms CreateObjectMapping: 0.080800 ms MarkObjects: 1.709700 ms  DeleteObjects: 2.103300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0